#!/usr/bin/env python3
"""
Simple test to verify the enhanced AI agent system is working.
"""

import sys
from pathlib import Path

def test_basic_imports():
    """Test that basic modules can be imported."""
    print("Testing basic imports...")
    
    try:
        from utils.logging_setup import setup_logging
        print("✓ Logging setup imported")
    except Exception as e:
        print(f"✗ Logging setup failed: {e}")
        return False
    
    try:
        from utils.console import console
        print("✓ Console imported")
    except Exception as e:
        print(f"✗ Console failed: {e}")
        return False
    
    try:
        from config import get_config
        config = get_config()
        print(f"✓ Config loaded - auto_confirm: {config.tools.auto_confirm}")
    except Exception as e:
        print(f"✗ Config failed: {e}")
        return False
    
    try:
        from message import create_user_message, create_assistant_message
        msg = create_user_message("test")
        print("✓ Message creation working")
    except Exception as e:
        print(f"✗ Message creation failed: {e}")
        return False
    
    return True

def test_tool_registry():
    """Test tool registry functionality."""
    print("Testing tool registry...")
    
    try:
        from tools import list_available_tools, get_tool
        tools = list_available_tools()
        print(f"✓ Available tools: {tools}")
        
        # Test getting a specific tool
        shell_tool = get_tool("shell")
        if shell_tool:
            print("✓ Shell tool available")
        else:
            print("✗ Shell tool not found")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Tool registry failed: {e}")
        return False

def test_chat_system():
    """Test that chat system can be imported."""
    print("Testing chat system...")
    
    try:
        from chat import generate_response
        print("✓ Chat system imported")
        return True
    except Exception as e:
        print(f"✗ Chat system failed: {e}")
        return False

def main():
    """Run simple tests."""
    print("=" * 50)
    print("SIMPLE AI AGENT SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_tool_registry,
        test_chat_system
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"RESULTS: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 Basic system tests passed!")
        return True
    else:
        print("❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
