"""
Web server for the AI agent.

This module provides a Flask-based web server with REST API
and web interface for the AI agent.
"""

try:
    from flask import Flask
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from config import get_config

def create_app() -> 'Flask':
    """
    Create and configure the Flask application.
    
    Returns:
        Configured Flask app
    """
    if not FLASK_AVAILABLE:
        raise ImportError("Flask not available. Install with: pip install flask flask-cors")
    
    config = get_config()
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = config.server.secret_key or 'dev-secret-key'
    app.config['MAX_CONTENT_LENGTH'] = config.server.max_content_length
    
    # Enable CORS if configured
    if config.server.cors_enabled:
        CORS(app)
    
    # Register blueprints
    from server.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from server.web import web_bp
    app.register_blueprint(web_bp)
    
    return app

__all__ = ["create_app"]
