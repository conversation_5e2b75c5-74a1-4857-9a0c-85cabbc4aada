"""
Message handling for the AI agent.

This module provides the core Message class for handling conversation messages
with support for different roles, metadata, and content processing.
"""

import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path

@dataclass
class Message:
    """
    Represents a message in a conversation.
    
    Attributes:
        role: The role of the message sender ('user', 'assistant', 'system')
        content: The message content
        timestamp: When the message was created
        call_id: Optional ID for tool calls
        quiet: Whether to suppress output
        pinned: Whether the message is pinned
        metadata: Additional metadata
    """
    role: str
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    call_id: Optional[str] = None
    quiet: bool = False
    pinned: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate message after initialization."""
        if self.role not in ['user', 'assistant', 'system']:
            raise ValueError(f"Invalid role: {self.role}")
        
        if not isinstance(self.content, str):
            raise ValueError("Content must be a string")
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create a Message from a dictionary."""
        # Handle timestamp conversion
        timestamp = data.get('timestamp')
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
        elif timestamp is None:
            timestamp = datetime.now()
        
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=timestamp,
            call_id=data.get('call_id'),
            quiet=data.get('quiet', False),
            pinned=data.get('pinned', False),
            metadata=data.get('metadata', {})
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Message to dictionary."""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'call_id': self.call_id,
            'quiet': self.quiet,
            'pinned': self.pinned,
            'metadata': self.metadata
        }
    
    def to_json(self) -> str:
        """Convert Message to JSON string."""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        """Create Message from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def replace(self, **kwargs) -> 'Message':
        """Create a new Message with replaced attributes."""
        data = self.to_dict()
        data.update(kwargs)
        return self.from_dict(data)
    
    def is_tool_call(self) -> bool:
        """Check if this message contains tool calls."""
        return self.call_id is not None
    
    def is_from_user(self) -> bool:
        """Check if this message is from a user."""
        return self.role == 'user'
    
    def is_from_assistant(self) -> bool:
        """Check if this message is from the assistant."""
        return self.role == 'assistant'
    
    def is_system(self) -> bool:
        """Check if this is a system message."""
        return self.role == 'system'
    
    def get_word_count(self) -> int:
        """Get the word count of the message content."""
        return len(self.content.split())
    
    def get_char_count(self) -> int:
        """Get the character count of the message content."""
        return len(self.content)
    
    def truncate(self, max_length: int = 1000) -> 'Message':
        """Create a truncated version of the message."""
        if len(self.content) <= max_length:
            return self
        
        truncated_content = self.content[:max_length] + "... [truncated]"
        return self.replace(content=truncated_content)
    
    def add_metadata(self, key: str, value: Any) -> 'Message':
        """Add metadata to the message."""
        new_metadata = self.metadata.copy()
        new_metadata[key] = value
        return self.replace(metadata=new_metadata)
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value."""
        return self.metadata.get(key, default)
    
    def __str__(self) -> str:
        """String representation of the message."""
        return f"Message(role={self.role}, content={self.content[:50]}...)"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"Message(role='{self.role}', content='{self.content[:30]}...', "
                f"timestamp={self.timestamp}, call_id={self.call_id})")


def create_user_message(content: str, **kwargs) -> Message:
    """Create a user message."""
    return Message(role='user', content=content, **kwargs)


def create_assistant_message(content: str, **kwargs) -> Message:
    """Create an assistant message."""
    return Message(role='assistant', content=content, **kwargs)


def create_system_message(content: str, **kwargs) -> Message:
    """Create a system message."""
    return Message(role='system', content=content, **kwargs)


def messages_to_dict_list(messages: List[Message]) -> List[Dict[str, Any]]:
    """Convert a list of messages to a list of dictionaries."""
    return [msg.to_dict() for msg in messages]


def messages_from_dict_list(data: List[Dict[str, Any]]) -> List[Message]:
    """Create a list of messages from a list of dictionaries."""
    return [Message.from_dict(item) for item in data]


def save_messages_to_file(messages: List[Message], filepath: Union[str, Path]) -> None:
    """Save messages to a JSON file."""
    filepath = Path(filepath)
    data = messages_to_dict_list(messages)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def load_messages_from_file(filepath: Union[str, Path]) -> List[Message]:
    """Load messages from a JSON file."""
    filepath = Path(filepath)
    
    if not filepath.exists():
        return []
    
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return messages_from_dict_list(data)
