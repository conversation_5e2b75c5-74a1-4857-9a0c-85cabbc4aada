"""
Advanced Intelligence and Reasoning System for AI Agent.

This module provides sophisticated decision-making capabilities,
step-by-step reasoning, and learning from interactions.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

class ReasoningType(Enum):
    """Types of reasoning processes."""
    ANALYTICAL = "analytical"
    CREATIVE = "creative"
    PROBLEM_SOLVING = "problem_solving"
    DECISION_MAKING = "decision_making"
    LEARNING = "learning"

class ConfidenceLevel(Enum):
    """Confidence levels for decisions."""
    LOW = 0.3
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95

@dataclass
class ReasoningStep:
    """Represents a single step in the reasoning process."""
    step_id: int
    description: str
    reasoning_type: ReasoningType
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    confidence: float
    timestamp: float
    duration: float

@dataclass
class Decision:
    """Represents a decision made by the AI agent."""
    decision_id: str
    context: str
    options: List[Dict[str, Any]]
    chosen_option: Dict[str, Any]
    reasoning_steps: List[ReasoningStep]
    confidence: float
    expected_outcome: str
    actual_outcome: Optional[str] = None
    success: Optional[bool] = None
    timestamp: float = 0.0

class IntelligenceEngine:
    """Advanced intelligence and reasoning engine for the AI agent."""
    
    def __init__(self, memory_file: str = "intelligence_memory.json"):
        self.memory_file = Path(memory_file)
        self.decisions_history: List[Decision] = []
        self.learned_patterns: Dict[str, Any] = {}
        self.performance_metrics: Dict[str, float] = {
            "success_rate": 0.0,
            "average_confidence": 0.0,
            "learning_rate": 0.0
        }
        self._load_memory()
    
    def analyze_situation(self, context: str, available_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the current situation and provide insights.
        
        Args:
            context: Description of the current situation
            available_data: Available data for analysis
            
        Returns:
            Analysis results with insights and recommendations
        """
        start_time = time.time()
        
        # Step 1: Context Analysis
        context_analysis = self._analyze_context(context, available_data)
        
        # Step 2: Pattern Recognition
        patterns = self._recognize_patterns(context, available_data)
        
        # Step 3: Risk Assessment
        risks = self._assess_risks(context, available_data)
        
        # Step 4: Opportunity Identification
        opportunities = self._identify_opportunities(context, available_data)
        
        # Step 5: Generate Insights
        insights = self._generate_insights(context_analysis, patterns, risks, opportunities)
        
        analysis_time = time.time() - start_time
        
        return {
            "context_analysis": context_analysis,
            "recognized_patterns": patterns,
            "risk_assessment": risks,
            "opportunities": opportunities,
            "insights": insights,
            "analysis_time": analysis_time,
            "confidence": self._calculate_analysis_confidence(insights)
        }
    
    def make_decision(self, context: str, options: List[Dict[str, Any]], 
                     constraints: Optional[Dict[str, Any]] = None) -> Decision:
        """
        Make an intelligent decision based on context and options.
        
        Args:
            context: Decision context
            options: Available options to choose from
            constraints: Any constraints to consider
            
        Returns:
            Decision object with reasoning
        """
        start_time = time.time()
        decision_id = f"decision_{int(time.time() * 1000)}"
        
        reasoning_steps = []
        
        # Step 1: Evaluate each option
        step_1_start = time.time()
        evaluated_options = []
        
        for i, option in enumerate(options):
            evaluation = self._evaluate_option(option, context, constraints)
            evaluated_options.append({
                **option,
                "evaluation": evaluation
            })
        
        reasoning_steps.append(ReasoningStep(
            step_id=1,
            description="Evaluate all available options",
            reasoning_type=ReasoningType.ANALYTICAL,
            input_data={"options": options, "context": context},
            output_data={"evaluated_options": evaluated_options},
            confidence=0.8,
            timestamp=step_1_start,
            duration=time.time() - step_1_start
        ))
        
        # Step 2: Apply learned patterns
        step_2_start = time.time()
        pattern_insights = self._apply_learned_patterns(context, evaluated_options)
        
        reasoning_steps.append(ReasoningStep(
            step_id=2,
            description="Apply learned patterns from previous decisions",
            reasoning_type=ReasoningType.LEARNING,
            input_data={"evaluated_options": evaluated_options},
            output_data={"pattern_insights": pattern_insights},
            confidence=0.7,
            timestamp=step_2_start,
            duration=time.time() - step_2_start
        ))
        
        # Step 3: Make final decision
        step_3_start = time.time()
        chosen_option = self._select_best_option(evaluated_options, pattern_insights)
        
        reasoning_steps.append(ReasoningStep(
            step_id=3,
            description="Select the best option based on evaluation and patterns",
            reasoning_type=ReasoningType.DECISION_MAKING,
            input_data={"evaluated_options": evaluated_options, "patterns": pattern_insights},
            output_data={"chosen_option": chosen_option},
            confidence=0.9,
            timestamp=step_3_start,
            duration=time.time() - step_3_start
        ))
        
        # Calculate overall confidence
        overall_confidence = sum(step.confidence for step in reasoning_steps) / len(reasoning_steps)
        
        # Create decision object
        decision = Decision(
            decision_id=decision_id,
            context=context,
            options=options,
            chosen_option=chosen_option,
            reasoning_steps=reasoning_steps,
            confidence=overall_confidence,
            expected_outcome=chosen_option.get("expected_outcome", "Unknown"),
            timestamp=start_time
        )
        
        # Store decision for learning
        self.decisions_history.append(decision)
        self._save_memory()
        
        return decision
    
    def learn_from_outcome(self, decision_id: str, actual_outcome: str, success: bool):
        """
        Learn from the outcome of a previous decision.
        
        Args:
            decision_id: ID of the decision to learn from
            actual_outcome: What actually happened
            success: Whether the decision was successful
        """
        # Find the decision
        decision = None
        for d in self.decisions_history:
            if d.decision_id == decision_id:
                decision = d
                break
        
        if not decision:
            return
        
        # Update decision with outcome
        decision.actual_outcome = actual_outcome
        decision.success = success
        
        # Extract learning patterns
        self._extract_learning_patterns(decision)
        
        # Update performance metrics
        self._update_performance_metrics()
        
        # Save updated memory
        self._save_memory()
    
    def get_reasoning_explanation(self, decision: Decision) -> str:
        """
        Generate a human-readable explanation of the reasoning process.
        
        Args:
            decision: Decision to explain
            
        Returns:
            Formatted explanation string
        """
        explanation = f"Decision Analysis for: {decision.context}\n"
        explanation += f"Confidence Level: {decision.confidence:.2%}\n\n"
        
        explanation += "Reasoning Process:\n"
        for step in decision.reasoning_steps:
            explanation += f"  Step {step.step_id}: {step.description}\n"
            explanation += f"    Type: {step.reasoning_type.value}\n"
            explanation += f"    Confidence: {step.confidence:.2%}\n"
            explanation += f"    Duration: {step.duration:.3f}s\n\n"
        
        explanation += f"Chosen Option: {decision.chosen_option.get('name', 'Unknown')}\n"
        explanation += f"Expected Outcome: {decision.expected_outcome}\n"
        
        if decision.actual_outcome:
            explanation += f"Actual Outcome: {decision.actual_outcome}\n"
            explanation += f"Success: {'Yes' if decision.success else 'No'}\n"
        
        return explanation
    
    def _analyze_context(self, context: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the context of the situation."""
        return {
            "complexity": self._assess_complexity(context, data),
            "urgency": self._assess_urgency(context, data),
            "importance": self._assess_importance(context, data),
            "resources_needed": self._identify_resources(context, data)
        }
    
    def _recognize_patterns(self, context: str, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Recognize patterns from previous experiences."""
        patterns = []
        
        # Look for similar contexts in history
        for decision in self.decisions_history:
            similarity = self._calculate_similarity(context, decision.context)
            if similarity > 0.7:
                patterns.append({
                    "pattern_type": "similar_context",
                    "similarity": similarity,
                    "previous_decision": decision.decision_id,
                    "success": decision.success,
                    "confidence": decision.confidence
                })
        
        return patterns
    
    def _assess_risks(self, context: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess potential risks in the situation."""
        return {
            "technical_risk": 0.3,  # Placeholder - would be calculated based on context
            "time_risk": 0.2,
            "resource_risk": 0.1,
            "overall_risk": 0.2
        }
    
    def _identify_opportunities(self, context: str, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify opportunities in the current situation."""
        return [
            {"type": "efficiency_improvement", "potential": 0.8},
            {"type": "learning_opportunity", "potential": 0.6},
            {"type": "automation_potential", "potential": 0.7}
        ]
    
    def _generate_insights(self, context_analysis: Dict, patterns: List, 
                          risks: Dict, opportunities: List) -> List[str]:
        """Generate actionable insights from analysis."""
        insights = []
        
        if context_analysis["complexity"] > 0.7:
            insights.append("This is a complex situation that may require breaking down into smaller steps")
        
        if context_analysis["urgency"] > 0.8:
            insights.append("High urgency detected - prioritize quick, reliable solutions")
        
        if len(patterns) > 0:
            insights.append(f"Found {len(patterns)} similar patterns from previous experiences")
        
        if risks["overall_risk"] > 0.5:
            insights.append("Significant risks detected - consider mitigation strategies")
        
        return insights
    
    def _calculate_analysis_confidence(self, insights: List[str]) -> float:
        """Calculate confidence level for the analysis."""
        base_confidence = 0.7
        insight_bonus = min(len(insights) * 0.05, 0.2)
        return min(base_confidence + insight_bonus, 0.95)
    
    def _evaluate_option(self, option: Dict[str, Any], context: str, 
                        constraints: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Evaluate a single option."""
        return {
            "feasibility": 0.8,  # Placeholder - would be calculated
            "effectiveness": 0.7,
            "efficiency": 0.6,
            "risk_level": 0.3,
            "overall_score": 0.7
        }
    
    def _apply_learned_patterns(self, context: str, options: List[Dict]) -> Dict[str, Any]:
        """Apply learned patterns to current decision."""
        return {
            "pattern_matches": 2,
            "confidence_boost": 0.1,
            "recommended_adjustments": []
        }
    
    def _select_best_option(self, evaluated_options: List[Dict], 
                           pattern_insights: Dict) -> Dict[str, Any]:
        """Select the best option from evaluated options."""
        # Simple selection based on overall score
        best_option = max(evaluated_options, 
                         key=lambda x: x["evaluation"]["overall_score"])
        return best_option
    
    def _extract_learning_patterns(self, decision: Decision):
        """Extract learning patterns from a completed decision."""
        pattern_key = f"context_{hash(decision.context) % 1000}"
        
        if pattern_key not in self.learned_patterns:
            self.learned_patterns[pattern_key] = {
                "contexts": [],
                "success_rate": 0.0,
                "common_factors": []
            }
        
        pattern = self.learned_patterns[pattern_key]
        pattern["contexts"].append({
            "decision_id": decision.decision_id,
            "success": decision.success,
            "confidence": decision.confidence
        })
        
        # Update success rate
        successes = sum(1 for ctx in pattern["contexts"] if ctx["success"])
        pattern["success_rate"] = successes / len(pattern["contexts"])
    
    def _update_performance_metrics(self):
        """Update overall performance metrics."""
        if not self.decisions_history:
            return
        
        completed_decisions = [d for d in self.decisions_history if d.success is not None]
        
        if completed_decisions:
            successes = sum(1 for d in completed_decisions if d.success)
            self.performance_metrics["success_rate"] = successes / len(completed_decisions)
            
            avg_confidence = sum(d.confidence for d in completed_decisions) / len(completed_decisions)
            self.performance_metrics["average_confidence"] = avg_confidence
            
            # Simple learning rate calculation
            recent_decisions = completed_decisions[-10:]  # Last 10 decisions
            if len(recent_decisions) >= 5:
                recent_success = sum(1 for d in recent_decisions if d.success) / len(recent_decisions)
                overall_success = self.performance_metrics["success_rate"]
                self.performance_metrics["learning_rate"] = max(0, recent_success - overall_success)
    
    def _assess_complexity(self, context: str, data: Dict[str, Any]) -> float:
        """Assess the complexity of the situation."""
        # Simple heuristic - would be more sophisticated in practice
        complexity_factors = len(data.keys()) + len(context.split())
        return min(complexity_factors / 100.0, 1.0)
    
    def _assess_urgency(self, context: str, data: Dict[str, Any]) -> float:
        """Assess the urgency of the situation."""
        urgency_keywords = ["urgent", "immediate", "asap", "critical", "emergency"]
        urgency_score = sum(1 for keyword in urgency_keywords if keyword in context.lower())
        return min(urgency_score / 3.0, 1.0)
    
    def _assess_importance(self, context: str, data: Dict[str, Any]) -> float:
        """Assess the importance of the situation."""
        importance_keywords = ["important", "critical", "essential", "vital", "crucial"]
        importance_score = sum(1 for keyword in importance_keywords if keyword in context.lower())
        return min(importance_score / 3.0, 1.0)
    
    def _identify_resources(self, context: str, data: Dict[str, Any]) -> List[str]:
        """Identify resources needed for the situation."""
        return ["time", "computational_power", "tools", "data"]
    
    def _calculate_similarity(self, context1: str, context2: str) -> float:
        """Calculate similarity between two contexts."""
        words1 = set(context1.lower().split())
        words2 = set(context2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _load_memory(self):
        """Load intelligence memory from file."""
        if self.memory_file.exists():
            try:
                with open(self.memory_file, 'r') as f:
                    data = json.load(f)
                    
                # Load decisions history
                if "decisions_history" in data:
                    self.decisions_history = [
                        Decision(**decision_data) 
                        for decision_data in data["decisions_history"]
                    ]
                
                # Load learned patterns
                if "learned_patterns" in data:
                    self.learned_patterns = data["learned_patterns"]
                
                # Load performance metrics
                if "performance_metrics" in data:
                    self.performance_metrics.update(data["performance_metrics"])
                    
            except Exception:
                # If loading fails, start with empty memory
                pass
    
    def _save_memory(self):
        """Save intelligence memory to file."""
        try:
            data = {
                "decisions_history": [asdict(decision) for decision in self.decisions_history],
                "learned_patterns": self.learned_patterns,
                "performance_metrics": self.performance_metrics
            }
            
            with open(self.memory_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception:
            # Silent failure - don't interrupt operation
            pass

# Global intelligence engine instance
_intelligence_engine: Optional[IntelligenceEngine] = None

def get_intelligence_engine() -> IntelligenceEngine:
    """Get the global intelligence engine instance."""
    global _intelligence_engine
    if _intelligence_engine is None:
        _intelligence_engine = IntelligenceEngine()
    return _intelligence_engine
