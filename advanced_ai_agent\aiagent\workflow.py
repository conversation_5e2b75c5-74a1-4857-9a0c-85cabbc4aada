"""
Advanced workflow engine for the AI agent.

This module provides intelligent workflow management with step tracking,
progress monitoring, predictive next steps, and context-aware automation.
"""

import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable, Generator
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from message import Message
from config import get_config

logger = logging.getLogger(__name__)


class StepStatus(Enum):
    """Status of a workflow step."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class WorkflowPriority(Enum):
    """Priority levels for workflow steps."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class WorkflowStep:
    """Represents a single step in a workflow."""
    id: str
    name: str
    description: str
    tool: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    status: StepStatus = StepStatus.PENDING
    priority: WorkflowPriority = WorkflowPriority.NORMAL
    dependencies: List[str] = field(default_factory=list)
    estimated_duration: int = 30  # seconds
    actual_duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Message] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_ready(self) -> bool:
        """Check if step is ready to execute (all dependencies completed)."""
        return self.status == StepStatus.PENDING
    
    @property
    def is_running(self) -> bool:
        """Check if step is currently running."""
        return self.status == StepStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """Check if step completed successfully."""
        return self.status == StepStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if step failed."""
        return self.status == StepStatus.FAILED


@dataclass
class WorkflowContext:
    """Context information for workflow execution."""
    user_goal: str
    current_directory: Path
    environment: Dict[str, str] = field(default_factory=dict)
    variables: Dict[str, Any] = field(default_factory=dict)
    files_created: List[str] = field(default_factory=list)
    files_modified: List[str] = field(default_factory=list)
    commands_executed: List[str] = field(default_factory=list)
    progress_percentage: float = 0.0
    estimated_completion: Optional[datetime] = None


class WorkflowEngine:
    """
    Advanced workflow engine with predictive capabilities.
    
    Features:
    - Step-by-step execution with dependency management
    - Progress tracking and estimation
    - Predictive next step suggestions
    - Context-aware automation
    - Parallel execution support
    - Error handling and recovery
    """
    
    def __init__(self):
        """Initialize the workflow engine."""
        self.workflows: Dict[str, List[WorkflowStep]] = {}
        self.contexts: Dict[str, WorkflowContext] = {}
        self.active_workflows: Dict[str, str] = {}  # session_id -> workflow_id
        self.step_predictors: List[Callable] = []
        self.execution_history: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
        
        # Register built-in predictors
        self._register_predictors()
    
    def create_workflow(
        self,
        workflow_id: str,
        user_goal: str,
        initial_steps: Optional[List[WorkflowStep]] = None
    ) -> str:
        """
        Create a new workflow.
        
        Args:
            workflow_id: Unique workflow identifier
            user_goal: User's goal description
            initial_steps: Initial workflow steps
        
        Returns:
            Workflow ID
        """
        with self._lock:
            self.workflows[workflow_id] = initial_steps or []
            self.contexts[workflow_id] = WorkflowContext(
                user_goal=user_goal,
                current_directory=Path.cwd()
            )
        
        logger.info(f"Created workflow: {workflow_id}")
        return workflow_id
    
    def add_step(
        self,
        workflow_id: str,
        step: WorkflowStep,
        position: Optional[int] = None
    ) -> None:
        """Add a step to a workflow."""
        with self._lock:
            if workflow_id not in self.workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            if position is None:
                self.workflows[workflow_id].append(step)
            else:
                self.workflows[workflow_id].insert(position, step)
        
        logger.debug(f"Added step {step.id} to workflow {workflow_id}")
    
    def execute_next_step(
        self,
        workflow_id: str,
        session_id: Optional[str] = None
    ) -> Optional[WorkflowStep]:
        """
        Execute the next ready step in the workflow.
        
        Args:
            workflow_id: Workflow to execute
            session_id: Session identifier
        
        Returns:
            Executed step or None if no steps ready
        """
        with self._lock:
            if workflow_id not in self.workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            # Find next ready step
            next_step = self._find_next_step(workflow_id)
            if not next_step:
                return None
            
            # Mark as running
            next_step.status = StepStatus.RUNNING
            next_step.start_time = datetime.now()
            
            # Track active workflow
            if session_id:
                self.active_workflows[session_id] = workflow_id
        
        try:
            # Execute step
            logger.info(f"Executing step: {next_step.name}")
            result = self._execute_step(workflow_id, next_step)
            
            # Update step status
            with self._lock:
                next_step.status = StepStatus.COMPLETED
                next_step.end_time = datetime.now()
                next_step.result = result
                next_step.actual_duration = int(
                    (next_step.end_time - next_step.start_time).total_seconds()
                )
                
                # Update context
                self._update_context(workflow_id, next_step, result)
                
                # Update progress
                self._update_progress(workflow_id)
            
            # Record execution
            self._record_execution(workflow_id, next_step, True)
            
            return next_step
            
        except Exception as e:
            # Handle failure
            with self._lock:
                next_step.status = StepStatus.FAILED
                next_step.end_time = datetime.now()
                next_step.error = str(e)
            
            self._record_execution(workflow_id, next_step, False, str(e))
            logger.error(f"Step {next_step.id} failed: {e}")
            raise
    
    def predict_next_steps(
        self,
        workflow_id: str,
        max_predictions: int = 3
    ) -> List[WorkflowStep]:
        """
        Predict the next steps based on current context and history.
        
        Args:
            workflow_id: Workflow to analyze
            max_predictions: Maximum number of predictions
        
        Returns:
            List of predicted next steps
        """
        if workflow_id not in self.workflows:
            return []
        
        context = self.contexts[workflow_id]
        completed_steps = [s for s in self.workflows[workflow_id] if s.is_completed]
        
        predictions = []
        
        # Run all predictors
        for predictor in self.step_predictors:
            try:
                predicted_steps = predictor(context, completed_steps)
                predictions.extend(predicted_steps)
            except Exception as e:
                logger.warning(f"Predictor failed: {e}")
        
        # Remove duplicates and limit results
        unique_predictions = []
        seen_names = set()
        
        for step in predictions:
            if step.name not in seen_names:
                unique_predictions.append(step)
                seen_names.add(step.name)
                
                if len(unique_predictions) >= max_predictions:
                    break
        
        return unique_predictions
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get comprehensive workflow status."""
        if workflow_id not in self.workflows:
            return {}
        
        steps = self.workflows[workflow_id]
        context = self.contexts[workflow_id]
        
        total_steps = len(steps)
        completed_steps = len([s for s in steps if s.is_completed])
        failed_steps = len([s for s in steps if s.is_failed])
        running_steps = len([s for s in steps if s.is_running])
        
        return {
            "workflow_id": workflow_id,
            "user_goal": context.user_goal,
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "running_steps": running_steps,
            "progress_percentage": context.progress_percentage,
            "estimated_completion": context.estimated_completion,
            "current_directory": str(context.current_directory),
            "files_created": context.files_created,
            "files_modified": context.files_modified,
            "commands_executed": context.commands_executed[-10:],  # Last 10
            "next_predictions": [
                {"name": step.name, "description": step.description}
                for step in self.predict_next_steps(workflow_id)
            ]
        }
    
    def _find_next_step(self, workflow_id: str) -> Optional[WorkflowStep]:
        """Find the next step ready for execution."""
        steps = self.workflows[workflow_id]
        
        # Find highest priority ready step
        ready_steps = [s for s in steps if s.is_ready]
        if not ready_steps:
            return None
        
        # Sort by priority and return highest
        ready_steps.sort(key=lambda s: s.priority.value, reverse=True)
        return ready_steps[0]
    
    def _execute_step(self, workflow_id: str, step: WorkflowStep) -> Message:
        """Execute a single workflow step."""
        # This would integrate with the tools system
        # For now, return a mock result
        return Message(
            role="system",
            content=f"Executed step: {step.name}",
            metadata={"step_id": step.id, "tool": step.tool}
        )
    
    def _update_context(
        self,
        workflow_id: str,
        step: WorkflowStep,
        result: Message
    ) -> None:
        """Update workflow context based on step execution."""
        context = self.contexts[workflow_id]
        
        # Update based on tool type
        if step.tool == "shell":
            context.commands_executed.append(step.parameters.get("command", ""))
        elif step.tool == "file" and "write" in step.parameters.get("operation", ""):
            file_path = step.parameters.get("path", "")
            if file_path:
                context.files_created.append(file_path)
        elif step.tool == "file" and "edit" in step.parameters.get("operation", ""):
            file_path = step.parameters.get("path", "")
            if file_path:
                context.files_modified.append(file_path)
    
    def _update_progress(self, workflow_id: str) -> None:
        """Update workflow progress."""
        steps = self.workflows[workflow_id]
        context = self.contexts[workflow_id]
        
        if not steps:
            context.progress_percentage = 100.0
            return
        
        completed = len([s for s in steps if s.is_completed])
        context.progress_percentage = (completed / len(steps)) * 100
        
        # Estimate completion time
        if completed > 0:
            avg_duration = sum(
                s.actual_duration for s in steps 
                if s.is_completed and s.actual_duration
            ) / completed
            
            remaining_steps = len(steps) - completed
            estimated_seconds = remaining_steps * avg_duration
            
            context.estimated_completion = datetime.now().timestamp() + estimated_seconds
    
    def _record_execution(
        self,
        workflow_id: str,
        step: WorkflowStep,
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """Record step execution for learning."""
        record = {
            "workflow_id": workflow_id,
            "step_id": step.id,
            "step_name": step.name,
            "tool": step.tool,
            "success": success,
            "duration": step.actual_duration,
            "timestamp": datetime.now().isoformat(),
            "error": error
        }
        
        self.execution_history.append(record)
        
        # Keep only recent history
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
    
    def _register_predictors(self) -> None:
        """Register built-in step predictors."""
        self.step_predictors.extend([
            self._predict_file_operations,
            self._predict_development_workflow,
            self._predict_testing_steps,
            self._predict_deployment_steps
        ])
    
    def _predict_file_operations(
        self,
        context: WorkflowContext,
        completed_steps: List[WorkflowStep]
    ) -> List[WorkflowStep]:
        """Predict file operation steps."""
        predictions = []
        
        # If files were created, suggest testing or documentation
        if context.files_created:
            predictions.append(WorkflowStep(
                id="test_created_files",
                name="Test Created Files",
                description="Run tests on newly created files",
                tool="code",
                parameters={"language": "python", "code": "# Run tests"}
            ))
        
        return predictions
    
    def _predict_development_workflow(
        self,
        context: WorkflowContext,
        completed_steps: List[WorkflowStep]
    ) -> List[WorkflowStep]:
        """Predict development workflow steps."""
        predictions = []
        
        # Common development patterns
        step_names = [s.name.lower() for s in completed_steps]
        
        if any("create" in name for name in step_names):
            predictions.append(WorkflowStep(
                id="add_documentation",
                name="Add Documentation",
                description="Add documentation for created components",
                tool="file",
                parameters={"operation": "write", "path": "README.md"}
            ))
        
        return predictions
    
    def _predict_testing_steps(
        self,
        context: WorkflowContext,
        completed_steps: List[WorkflowStep]
    ) -> List[WorkflowStep]:
        """Predict testing-related steps."""
        predictions = []
        
        # If code was written, suggest tests
        if any(s.tool == "code" for s in completed_steps):
            predictions.append(WorkflowStep(
                id="write_tests",
                name="Write Unit Tests",
                description="Create unit tests for the implemented functionality",
                tool="code",
                parameters={"language": "python", "code": "# Write unit tests"}
            ))
        
        return predictions
    
    def _predict_deployment_steps(
        self,
        context: WorkflowContext,
        completed_steps: List[WorkflowStep]
    ) -> List[WorkflowStep]:
        """Predict deployment-related steps."""
        predictions = []
        
        # If project structure is complete, suggest deployment
        if len(completed_steps) > 5:
            predictions.append(WorkflowStep(
                id="prepare_deployment",
                name="Prepare for Deployment",
                description="Set up deployment configuration and scripts",
                tool="file",
                parameters={"operation": "write", "path": "deploy.sh"}
            ))
        
        return predictions


# Global workflow engine instance
_workflow_engine: Optional[WorkflowEngine] = None


def get_workflow_engine() -> WorkflowEngine:
    """Get the global workflow engine instance."""
    global _workflow_engine
    if _workflow_engine is None:
        _workflow_engine = WorkflowEngine()
    return _workflow_engine


__all__ = [
    "WorkflowEngine",
    "WorkflowStep",
    "WorkflowContext",
    "StepStatus",
    "WorkflowPriority",
    "get_workflow_engine",
]
