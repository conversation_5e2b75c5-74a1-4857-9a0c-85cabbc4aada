"""
Advanced AI Agent - A comprehensive AI assistant powered by Gemini 2.0 Flash

This package provides a powerful AI agent with extensive tool capabilities,
interactive terminal interface, web UI, and REST API.
"""

__version__ = "1.0.0"
__author__ = "Advanced AI Agent Team"
__email__ = "<EMAIL>"
__description__ = "Advanced AI Agent powered by Gemini 2.0 Flash"

# Core imports
from message import Message
from config import Config, get_config
from chat import chat
from llm import reply, get_model

# Tool imports
from tools import (
    ToolSpec,
    ToolUse,
    ToolFormat,
    get_tools,
    init_tools,
    execute_msg,
)

__all__ = [
    # Core
    "Message",
    "Config", 
    "get_config",
    "chat",
    "reply",
    "get_model",
    
    # Tools
    "ToolSpec",
    "ToolUse", 
    "ToolFormat",
    "get_tools",
    "init_tools",
    "execute_msg",
    
    # Metadata
    "__version__",
    "__author__",
    "__email__",
    "__description__",
]
