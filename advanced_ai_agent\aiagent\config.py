"""
Configuration management for the AI agent.

This module handles configuration loading, validation, and management
for the AI agent system.
"""

import os
import toml
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from platformdirs import user_config_dir, user_data_dir


@dataclass
class LLMConfig:
    """Configuration for LLM settings."""
    model: str = "gemini-2.0-flash-exp"
    temperature: float = 0.7
    max_tokens: int = 8192
    top_p: float = 0.95
    top_k: int = 40
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    timeout: int = 60
    max_retries: int = 3


@dataclass
class ToolsConfig:
    """Configuration for tools - Enhanced for autonomous operation."""
    enabled: List[str] = field(default_factory=lambda: [
        "shell", "python", "code", "file", "web", "codebase",
        "browser", "computer", "github", "rag", "web_tools"
    ])
    auto_confirm: bool = True  # Enable autonomous operation
    timeout: int = 600  # Increased timeout for complex operations
    max_output_length: int = 50000  # Increased for comprehensive outputs


@dataclass
class ServerConfig:
    """Configuration for the web server."""
    host: str = "127.0.0.1"
    port: int = 5000
    debug: bool = False
    cors_enabled: bool = True
    max_content_length: int = 16 * 1024 * 1024  # 16MB
    secret_key: Optional[str] = None


@dataclass
class FeaturesConfig:
    """Configuration for optional features."""
    tts_enabled: bool = True
    telemetry_enabled: bool = False
    auto_commit: bool = False
    cost_tracking: bool = True
    conversation_backup: bool = True
    pre_commit_checks: bool = True


@dataclass
class Config:
    """Main configuration class."""
    llm: LLMConfig = field(default_factory=LLMConfig)
    tools: ToolsConfig = field(default_factory=ToolsConfig)
    server: ServerConfig = field(default_factory=ServerConfig)
    features: FeaturesConfig = field(default_factory=FeaturesConfig)
    
    # Paths
    config_dir: Path = field(default_factory=lambda: Path(user_config_dir("aiagent")))
    data_dir: Path = field(default_factory=lambda: Path(user_data_dir("aiagent")))
    logs_dir: Optional[Path] = None
    workspace_dir: Optional[Path] = None
    
    def __post_init__(self):
        """Initialize paths and create directories."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        if self.logs_dir is None:
            self.logs_dir = self.data_dir / "logs"
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        if self.workspace_dir is None:
            self.workspace_dir = Path.cwd()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Config':
        """Create Config from dictionary."""
        config = cls()
        
        # Update LLM config
        if 'llm' in data:
            llm_data = data['llm']
            for key, value in llm_data.items():
                if hasattr(config.llm, key):
                    setattr(config.llm, key, value)
        
        # Update tools config
        if 'tools' in data:
            tools_data = data['tools']
            for key, value in tools_data.items():
                if hasattr(config.tools, key):
                    setattr(config.tools, key, value)
        
        # Update server config
        if 'server' in data:
            server_data = data['server']
            for key, value in server_data.items():
                if hasattr(config.server, key):
                    setattr(config.server, key, value)
        
        # Update features config
        if 'features' in data:
            features_data = data['features']
            for key, value in features_data.items():
                if hasattr(config.features, key):
                    setattr(config.features, key, value)
        
        # Update paths
        if 'config_dir' in data:
            config.config_dir = Path(data['config_dir'])
        if 'data_dir' in data:
            config.data_dir = Path(data['data_dir'])
        if 'logs_dir' in data:
            config.logs_dir = Path(data['logs_dir'])
        if 'workspace_dir' in data:
            config.workspace_dir = Path(data['workspace_dir'])
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Config to dictionary."""
        return {
            'llm': {
                'model': self.llm.model,
                'temperature': self.llm.temperature,
                'max_tokens': self.llm.max_tokens,
                'top_p': self.llm.top_p,
                'top_k': self.llm.top_k,
                'timeout': self.llm.timeout,
                'max_retries': self.llm.max_retries,
            },
            'tools': {
                'enabled': self.tools.enabled,
                'auto_confirm': self.tools.auto_confirm,
                'timeout': self.tools.timeout,
                'max_output_length': self.tools.max_output_length,
            },
            'server': {
                'host': self.server.host,
                'port': self.server.port,
                'debug': self.server.debug,
                'cors_enabled': self.server.cors_enabled,
                'max_content_length': self.server.max_content_length,
            },
            'features': {
                'tts_enabled': self.features.tts_enabled,
                'telemetry_enabled': self.features.telemetry_enabled,
                'auto_commit': self.features.auto_commit,
                'cost_tracking': self.features.cost_tracking,
                'conversation_backup': self.features.conversation_backup,
                'pre_commit_checks': self.features.pre_commit_checks,
            },
            'config_dir': str(self.config_dir),
            'data_dir': str(self.data_dir),
            'logs_dir': str(self.logs_dir),
            'workspace_dir': str(self.workspace_dir),
        }
    
    def save(self, filepath: Optional[Union[str, Path]] = None) -> None:
        """Save configuration to file."""
        if filepath is None:
            filepath = self.config_dir / "config.toml"
        
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        with open(filepath, 'w') as f:
            toml.dump(self.to_dict(), f)
    
    @classmethod
    def load(cls, filepath: Optional[Union[str, Path]] = None) -> 'Config':
        """Load configuration from file."""
        if filepath is None:
            config_dir = Path(user_config_dir("aiagent"))
            filepath = config_dir / "config.toml"
        
        filepath = Path(filepath)
        
        if not filepath.exists():
            # Return default config if file doesn't exist
            return cls()
        
        with open(filepath, 'r') as f:
            data = toml.load(f)
        
        return cls.from_dict(data)
    
    def get_env(self, key: str, default: Any = None) -> Any:
        """Get environment variable with fallback."""
        return os.environ.get(key, default)
    
    def get_env_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable."""
        value = self.get_env(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def update_from_env(self) -> None:
        """Update configuration from environment variables."""
        # LLM settings
        if api_key := self.get_env("GEMINI_API_KEY"):
            self.llm.api_key = api_key
        if model := self.get_env("AIAGENT_MODEL"):
            self.llm.model = model
        if temp := self.get_env("AIAGENT_TEMPERATURE"):
            try:
                self.llm.temperature = float(temp)
            except ValueError:
                pass
        
        # Tools settings
        if tools := self.get_env("AIAGENT_TOOLS"):
            self.tools.enabled = [t.strip() for t in tools.split(",")]
        if self.get_env_bool("AIAGENT_AUTO_CONFIRM"):
            self.tools.auto_confirm = True
        
        # Server settings
        if host := self.get_env("AIAGENT_HOST"):
            self.server.host = host
        if port := self.get_env("AIAGENT_PORT"):
            try:
                self.server.port = int(port)
            except ValueError:
                pass
        
        # Features
        self.features.tts_enabled = self.get_env_bool("AIAGENT_TTS", self.features.tts_enabled)
        self.features.telemetry_enabled = self.get_env_bool("AIAGENT_TELEMETRY", self.features.telemetry_enabled)
        self.features.auto_commit = self.get_env_bool("AIAGENT_AUTO_COMMIT", self.features.auto_commit)


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config.load()
        _config.update_from_env()
    return _config


def set_config(config: Config) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config


def reload_config() -> Config:
    """Reload configuration from file and environment."""
    global _config
    _config = Config.load()
    _config.update_from_env()
    return _config
