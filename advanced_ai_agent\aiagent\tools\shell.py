"""
Advanced shell tool for executing terminal commands with enhanced capabilities.

This tool provides comprehensive terminal interaction including command execution,
process management, environment handling, and intelligent command suggestions.
"""

import os
import subprocess
import shlex
import logging
import signal
import threading
import time
import json
from typing import Generator, Optional, Dict, Any, List
from pathlib import Path
from collections import defaultdict

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


class AdvancedShellTool(ToolSpec):
    """Advanced tool for executing shell commands with enhanced capabilities."""

    def __init__(self):
        super().__init__(
            name="shell",
            description="Execute shell commands with advanced terminal capabilities, process management, and environment handling",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Shell action to perform",
                    required=False,
                    enum=["execute", "ps", "kill", "env", "which", "history", "suggest"],
                    default="execute"
                ),
                Parameter(
                    name="command",
                    type="string",
                    description="The shell command to execute",
                    required=False
                ),
                Parameter(
                    name="timeout",
                    type="integer",
                    description="Timeout in seconds (default: 60)",
                    required=False,
                    default=60
                ),
                Parameter(
                    name="cwd",
                    type="string",
                    description="Working directory for the command",
                    required=False
                ),
                Parameter(
                    name="env_vars",
                    type="string",
                    description="Environment variables (JSON format)",
                    required=False
                ),
                Parameter(
                    name="interactive",
                    type="boolean",
                    description="Run in interactive mode",
                    required=False,
                    default=False
                ),
                Parameter(
                    name="capture_output",
                    type="boolean",
                    description="Capture command output",
                    required=False,
                    default=True
                )
            ],
            block_types=["shell", "bash", "sh", "zsh", "fish", "cmd", "powershell", "terminal"]
        )
        self._processes: Dict[str, subprocess.Popen] = {}
        self._command_history: List[str] = []
        self._environment: Dict[str, str] = os.environ.copy()
        self._current_dir: Path = Path.cwd()
        self._lock = threading.Lock()
        self._command_suggestions = {
            "file_operations": ["ls", "cat", "grep", "find", "locate", "du", "df"],
            "process_management": ["ps", "top", "htop", "kill", "killall", "jobs"],
            "network": ["ping", "curl", "wget", "netstat", "ss", "nmap"],
            "system_info": ["uname", "whoami", "id", "uptime", "free", "lscpu"],
            "package_management": ["apt", "yum", "brew", "pip", "npm", "cargo"],
            "development": ["git", "docker", "make", "cmake", "gcc", "python", "node"]
        }
    
    def is_available(self) -> bool:
        """Check if shell is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute shell operations with advanced capabilities.

        Args:
            content: Shell command(s) or operation content
            **kwargs: Additional parameters

        Yields:
            Response messages with operation results
        """
        try:
            # Parse content if action not specified
            if "action" not in kwargs and "command" not in kwargs:
                action, command = self._parse_content(content)
                kwargs.update({
                    "action": action,
                    "command": command
                })

            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params.get("action", "execute")

            # Execute action
            if action == "execute":
                yield from self._execute_commands(params)
            elif action == "ps":
                yield from self._list_processes(params)
            elif action == "kill":
                yield from self._kill_process(params)
            elif action == "env":
                yield from self._manage_environment(params)
            elif action == "which":
                yield from self._which_command(params)
            elif action == "history":
                yield from self._show_history(params)
            elif action == "suggest":
                yield from self._suggest_commands(params)
            else:
                yield self.create_response(f"Unknown shell action: {action}")

        except Exception as e:
            logger.error(f"Error in shell operation: {e}")
            yield self.create_response(self.format_error(e))

    def _parse_content(self, content: str) -> tuple[str, str]:
        """Parse content to extract action and command."""
        content = content.strip()

        # Check for action prefixes
        if content.startswith("ps"):
            return "ps", content
        elif content.startswith("kill "):
            return "kill", content[5:].strip()
        elif content.startswith("env"):
            return "env", content
        elif content.startswith("which "):
            return "which", content[6:].strip()
        elif content.startswith("history"):
            return "history", ""
        elif content.startswith("suggest"):
            return "suggest", content[7:].strip()
        else:
            return "execute", content
    
    def _parse_commands(self, content: str) -> list[str]:
        """Parse multiple commands from content."""
        # Split by newlines and handle line continuations
        lines = content.split('\n')
        commands = []
        current_command = ""
        
        for line in lines:
            line = line.strip()
            
            # Skip comments and empty lines
            if not line or line.startswith('#'):
                continue
            
            # Handle line continuation
            if line.endswith('\\'):
                current_command += line[:-1] + " "
                continue
            
            current_command += line
            
            if current_command:
                commands.append(current_command)
                current_command = ""
        
        # Add any remaining command
        if current_command:
            commands.append(current_command)
        
        return commands
    
    def _execute_single_command(
        self,
        command: str,
        timeout: int = 60,
        cwd: Optional[str] = None,
        env: Optional[Dict[str, str]] = None,
        interactive: bool = False,
        capture_output: bool = True
    ) -> Generator[Message, None, None]:
        """Execute a single shell command."""
        try:
            # Determine shell and prepare command
            if os.name == 'nt':  # Windows
                shell_cmd = ['cmd', '/c', command]
                shell = True
            else:  # Unix-like
                shell_cmd = command
                shell = True
            
            # Set working directory
            if cwd:
                cwd_path = Path(cwd)
                if not cwd_path.exists():
                    yield self.create_response(f"Error: Directory '{cwd}' does not exist")
                    return
                cwd = str(cwd_path.resolve())
            
            # Log command execution
            logger.info(f"Executing command: {command}")
            yield self.create_response(f"$ {command}")
            
            # Execute command
            process = subprocess.Popen(
                shell_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                shell=shell,
                cwd=cwd,
                bufsize=1,
                universal_newlines=True
            )
            
            # Store process for potential termination
            process_id = str(id(process))
            with self._lock:
                self._processes[process_id] = process
            
            try:
                # Read output in real-time
                output_lines = []
                
                while True:
                    line = process.stdout.readline()
                    if line:
                        output_lines.append(line.rstrip())
                        # Yield partial output for long-running commands
                        if len(output_lines) >= 10:
                            yield self.create_response('\n'.join(output_lines))
                            output_lines = []
                    
                    # Check if process has finished
                    if process.poll() is not None:
                        break
                    
                    # Check timeout
                    if timeout and time.time() - process.poll() > timeout:
                        process.terminate()
                        yield self.create_response(f"Command timed out after {timeout} seconds")
                        return
                
                # Wait for process to complete
                return_code = process.wait(timeout=timeout)
                
                # Get any remaining output
                remaining_output = process.stdout.read()
                if remaining_output:
                    output_lines.extend(remaining_output.split('\n'))
                
                # Yield final output
                if output_lines:
                    final_output = '\n'.join(line for line in output_lines if line)
                    if final_output:
                        yield self.create_response(final_output)
                
                # Report exit code if non-zero
                if return_code != 0:
                    yield self.create_response(f"Command exited with code {return_code}")
                
            except subprocess.TimeoutExpired:
                process.kill()
                yield self.create_response(f"Command timed out after {timeout} seconds")
            except KeyboardInterrupt:
                process.terminate()
                yield self.create_response("Command interrupted")
            finally:
                # Clean up process reference
                with self._lock:
                    self._processes.pop(process_id, None)
                
                # Ensure process is terminated
                if process.poll() is None:
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
        
        except FileNotFoundError:
            yield self.create_response(f"Command not found: {command}")
        except PermissionError:
            yield self.create_response(f"Permission denied: {command}")
        except Exception as e:
            logger.error(f"Error executing command '{command}': {e}")
            yield self.create_response(f"Error: {str(e)}")
    
    def terminate_all_processes(self) -> None:
        """Terminate all running processes."""
        with self._lock:
            for process in self._processes.values():
                if process.poll() is None:
                    try:
                        process.terminate()
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    except Exception as e:
                        logger.error(f"Error terminating process: {e}")
            self._processes.clear()
    
    def get_running_processes(self) -> list[str]:
        """Get list of running process IDs."""
        with self._lock:
            return [pid for pid, process in self._processes.items() if process.poll() is None]

    def _execute_commands(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Execute shell commands with enhanced features."""
        command = params.get("command", "")
        if not command:
            yield self.create_response("Command is required for execute action")
            return

        timeout = params.get("timeout", 60)
        cwd = params.get("cwd")
        env_vars = params.get("env_vars")
        interactive = params.get("interactive", False)
        capture_output = params.get("capture_output", True)

        # Parse environment variables
        env = self._environment.copy()
        if env_vars:
            try:
                additional_env = json.loads(env_vars)
                env.update(additional_env)
            except json.JSONDecodeError:
                yield self.create_response("Invalid JSON format for environment variables")
                return

        # Add command to history
        self._command_history.append(command)
        if len(self._command_history) > 1000:
            self._command_history = self._command_history[-1000:]

        # Parse commands (handle multiple commands)
        commands = self._parse_commands(command)

        for cmd in commands:
            if not cmd.strip():
                continue

            yield from self._execute_single_command(
                cmd.strip(),
                timeout=timeout,
                cwd=cwd,
                env=env,
                interactive=interactive,
                capture_output=capture_output
            )

    def _list_processes(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """List running processes."""
        if not PSUTIL_AVAILABLE:
            yield self.create_response("Process listing requires psutil. Install with: pip install psutil")
            return

        try:
            processes = []

            # Get system processes
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)

            # Format output
            result = "🔄 Running Processes (Top 20 by CPU usage):\n\n"
            result += f"{'PID':<8} {'Name':<20} {'CPU%':<8} {'Memory%':<10} {'Status':<10}\n"
            result += "-" * 66 + "\n"

            for proc in processes[:20]:
                result += f"{proc['pid']:<8} {proc['name'][:19]:<20} {proc.get('cpu_percent', 0):<8.1f} {proc.get('memory_percent', 0):<10.1f} {proc.get('status', 'unknown'):<10}\n"

            # Show our managed processes
            with self._lock:
                if self._processes:
                    result += f"\n🎯 Managed Processes ({len(self._processes)}):\n"
                    for pid, process in self._processes.items():
                        status = "running" if process.poll() is None else "finished"
                        result += f"- {pid}: {status}\n"

            yield self.create_response(result)

        except Exception as e:
            yield self.create_response(f"Error listing processes: {e}")

    def _kill_process(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Kill a process by PID or name."""
        if not PSUTIL_AVAILABLE:
            yield self.create_response("Process management requires psutil. Install with: pip install psutil")
            return

        command = params.get("command", "")
        if not command:
            yield self.create_response("Process PID or name required for kill action")
            return

        try:
            # Try to parse as PID first
            try:
                pid = int(command)
                proc = psutil.Process(pid)
                proc.terminate()
                yield self.create_response(f"Terminated process {pid} ({proc.name()})")
            except ValueError:
                # Kill by name
                killed_count = 0
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if command.lower() in proc.info['name'].lower():
                            proc.terminate()
                            killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if killed_count > 0:
                    yield self.create_response(f"Terminated {killed_count} processes matching '{command}'")
                else:
                    yield self.create_response(f"No processes found matching '{command}'")

        except psutil.NoSuchProcess:
            yield self.create_response(f"Process {command} not found")
        except psutil.AccessDenied:
            yield self.create_response(f"Access denied when trying to kill process {command}")
        except Exception as e:
            yield self.create_response(f"Error killing process: {e}")

    def _manage_environment(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Manage environment variables."""
        command = params.get("command", "env")

        if command == "env":
            # Show all environment variables
            result = "🌍 Environment Variables:\n\n"
            for key, value in sorted(self._environment.items()):
                # Truncate long values
                display_value = value[:50] + "..." if len(value) > 50 else value
                result += f"{key}={display_value}\n"

            yield self.create_response(result)

        elif "=" in command:
            # Set environment variable
            key, value = command.split("=", 1)
            self._environment[key] = value
            yield self.create_response(f"Set environment variable: {key}={value}")

        else:
            # Show specific variable
            var_name = command.replace("env ", "").strip()
            if var_name in self._environment:
                yield self.create_response(f"{var_name}={self._environment[var_name]}")
            else:
                yield self.create_response(f"Environment variable '{var_name}' not found")

    def _which_command(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Find the location of a command."""
        command = params.get("command", "")
        if not command:
            yield self.create_response("Command name required for which action")
            return

        try:
            # Use shutil.which to find command
            import shutil
            location = shutil.which(command)

            if location:
                yield self.create_response(f"📍 {command} is located at: {location}")

                # Additional info
                try:
                    import stat
                    file_stat = os.stat(location)
                    permissions = stat.filemode(file_stat.st_mode)
                    size = file_stat.st_size

                    yield self.create_response(
                        f"📊 File info:\n"
                        f"- Permissions: {permissions}\n"
                        f"- Size: {size} bytes\n"
                        f"- Executable: {'Yes' if os.access(location, os.X_OK) else 'No'}"
                    )
                except Exception:
                    pass
            else:
                yield self.create_response(f"❌ Command '{command}' not found in PATH")

                # Suggest similar commands
                suggestions = self._find_similar_commands(command)
                if suggestions:
                    yield self.create_response(f"💡 Similar commands: {', '.join(suggestions)}")

        except Exception as e:
            yield self.create_response(f"Error finding command: {e}")

    def _show_history(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Show command history."""
        if not self._command_history:
            yield self.create_response("No command history available")
            return

        result = "📜 Command History (last 20):\n\n"
        recent_commands = self._command_history[-20:]

        for i, cmd in enumerate(recent_commands, 1):
            result += f"{i:2d}. {cmd}\n"

        yield self.create_response(result)

    def _suggest_commands(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Suggest commands based on context or query."""
        query = params.get("command", "").lower()

        if not query:
            # Show general suggestions
            result = "💡 Command Suggestions by Category:\n\n"
            for category, commands in self._command_suggestions.items():
                result += f"🔧 {category.replace('_', ' ').title()}:\n"
                result += f"   {', '.join(commands[:5])}\n\n"

            yield self.create_response(result)
        else:
            # Find relevant suggestions
            suggestions = []
            for category, commands in self._command_suggestions.items():
                for cmd in commands:
                    if query in cmd.lower() or cmd.lower().startswith(query):
                        suggestions.append(f"{cmd} ({category})")

            if suggestions:
                yield self.create_response(f"💡 Suggestions for '{query}':\n\n" + "\n".join(suggestions[:10]))
            else:
                yield self.create_response(f"No suggestions found for '{query}'")

    def _find_similar_commands(self, command: str) -> List[str]:
        """Find commands similar to the given command."""
        similar = []
        command_lower = command.lower()

        for category, commands in self._command_suggestions.items():
            for cmd in commands:
                if (command_lower in cmd.lower() or
                    cmd.lower().startswith(command_lower[:3]) or
                    any(c in cmd.lower() for c in command_lower.split())):
                    similar.append(cmd)

        return similar[:5]


# Create tool instance
shell_tool = AdvancedShellTool()
