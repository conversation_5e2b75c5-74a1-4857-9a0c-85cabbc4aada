"""
Performance Optimization and Efficiency System for AI Agent.

This module provides performance monitoring, optimization algorithms,
and efficient processing to reduce redundant operations.
"""

import time
import functools
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import hashlib
import json

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking."""
    operation_name: str
    execution_time: float
    memory_usage: int
    cpu_usage: float
    cache_hits: int = 0
    cache_misses: int = 0
    error_count: int = 0
    success_count: int = 0

class PerformanceCache:
    """High-performance caching system with TTL and LRU eviction."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.access_order = deque()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self.lock:
            if key in self.cache:
                value, expiry = self.cache[key]
                if time.time() < expiry:
                    # Move to end (most recently used)
                    self.access_order.remove(key)
                    self.access_order.append(key)
                    return value
                else:
                    # Expired
                    del self.cache[key]
                    self.access_order.remove(key)
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache."""
        with self.lock:
            ttl = ttl or self.default_ttl
            expiry = time.time() + ttl
            
            if key in self.cache:
                self.access_order.remove(key)
            elif len(self.cache) >= self.max_size:
                # Evict least recently used
                oldest_key = self.access_order.popleft()
                del self.cache[oldest_key]
            
            self.cache[key] = (value, expiry)
            self.access_order.append(key)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": 0.0,  # Would need to track hits/misses
                "memory_usage": sum(len(str(v[0])) for v in self.cache.values())
            }

class PerformanceOptimizer:
    """Advanced performance optimization system."""
    
    def __init__(self):
        self.cache = PerformanceCache()
        self.metrics: Dict[str, List[PerformanceMetrics]] = defaultdict(list)
        self.operation_counts: Dict[str, int] = defaultdict(int)
        self.error_patterns: Dict[str, int] = defaultdict(int)
        self.optimization_rules: List[Callable] = []
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Register default optimization rules
        self._register_default_rules()
    
    def cached(self, ttl: int = 3600, key_func: Optional[Callable] = None):
        """Decorator for caching function results."""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = self._generate_cache_key(func.__name__, args, kwargs)
                
                # Try to get from cache
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.cache.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    def timed(self, operation_name: Optional[str] = None):
        """Decorator for timing function execution."""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                op_name = operation_name or func.__name__
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # Record metrics
                    metrics = PerformanceMetrics(
                        operation_name=op_name,
                        execution_time=execution_time,
                        memory_usage=0,  # Would need psutil for accurate measurement
                        cpu_usage=0.0,
                        success_count=1
                    )
                    self.metrics[op_name].append(metrics)
                    self.operation_counts[op_name] += 1
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    
                    # Record error metrics
                    metrics = PerformanceMetrics(
                        operation_name=op_name,
                        execution_time=execution_time,
                        memory_usage=0,
                        cpu_usage=0.0,
                        error_count=1
                    )
                    self.metrics[op_name].append(metrics)
                    self.error_patterns[str(e)] += 1
                    
                    raise
            
            return wrapper
        return decorator
    
    def batch_process(self, items: List[Any], processor: Callable, 
                     batch_size: int = 10, max_workers: int = 4) -> List[Any]:
        """Process items in parallel batches for better performance."""
        if not items:
            return []
        
        # Split into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        
        # Process batches in parallel
        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_batch = {
                executor.submit(self._process_batch, batch, processor): batch 
                for batch in batches
            }
            
            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    results.extend(batch_results)
                except Exception as e:
                    # Log error but continue processing other batches
                    self.error_patterns[f"batch_processing: {str(e)}"] += 1
        
        return results
    
    def optimize_operation(self, operation_name: str) -> Dict[str, Any]:
        """Analyze and optimize a specific operation."""
        if operation_name not in self.metrics:
            return {"status": "no_data", "message": "No metrics available for this operation"}
        
        operation_metrics = self.metrics[operation_name]
        
        # Calculate statistics
        execution_times = [m.execution_time for m in operation_metrics]
        avg_time = sum(execution_times) / len(execution_times)
        max_time = max(execution_times)
        min_time = min(execution_times)
        
        total_errors = sum(m.error_count for m in operation_metrics)
        total_successes = sum(m.success_count for m in operation_metrics)
        error_rate = total_errors / (total_errors + total_successes) if (total_errors + total_successes) > 0 else 0
        
        # Generate optimization suggestions
        suggestions = []
        
        if avg_time > 1.0:
            suggestions.append("Consider caching results for this slow operation")
        
        if error_rate > 0.1:
            suggestions.append("High error rate detected - implement better error handling")
        
        if max_time > avg_time * 3:
            suggestions.append("Inconsistent performance - investigate outliers")
        
        if len(execution_times) > 100 and avg_time > 0.5:
            suggestions.append("Frequently used slow operation - consider optimization")
        
        return {
            "status": "analyzed",
            "statistics": {
                "avg_execution_time": avg_time,
                "max_execution_time": max_time,
                "min_execution_time": min_time,
                "total_calls": len(operation_metrics),
                "error_rate": error_rate
            },
            "suggestions": suggestions
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        total_operations = sum(self.operation_counts.values())
        
        # Find slowest operations
        slow_operations = []
        for op_name, metrics_list in self.metrics.items():
            if metrics_list:
                avg_time = sum(m.execution_time for m in metrics_list) / len(metrics_list)
                slow_operations.append((op_name, avg_time))
        
        slow_operations.sort(key=lambda x: x[1], reverse=True)
        
        # Find most error-prone operations
        error_operations = []
        for op_name, metrics_list in self.metrics.items():
            if metrics_list:
                error_count = sum(m.error_count for m in metrics_list)
                if error_count > 0:
                    error_operations.append((op_name, error_count))
        
        error_operations.sort(key=lambda x: x[1], reverse=True)
        
        return {
            "total_operations": total_operations,
            "cache_stats": self.cache.stats(),
            "slowest_operations": slow_operations[:5],
            "error_prone_operations": error_operations[:5],
            "common_errors": dict(list(self.error_patterns.items())[:5]),
            "optimization_opportunities": self._identify_optimization_opportunities()
        }
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate a cache key for function arguments."""
        key_data = {
            "function": func_name,
            "args": str(args),
            "kwargs": str(sorted(kwargs.items()))
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _process_batch(self, batch: List[Any], processor: Callable) -> List[Any]:
        """Process a single batch of items."""
        return [processor(item) for item in batch]
    
    def _register_default_rules(self):
        """Register default optimization rules."""
        
        def cache_slow_operations(metrics: PerformanceMetrics) -> Optional[str]:
            if metrics.execution_time > 2.0:
                return f"Consider caching results for {metrics.operation_name}"
            return None
        
        def parallel_processing_suggestion(metrics: PerformanceMetrics) -> Optional[str]:
            if metrics.execution_time > 5.0:
                return f"Consider parallel processing for {metrics.operation_name}"
            return None
        
        self.optimization_rules.extend([cache_slow_operations, parallel_processing_suggestion])
    
    def _identify_optimization_opportunities(self) -> List[str]:
        """Identify optimization opportunities based on metrics."""
        opportunities = []
        
        # Check for frequently called slow operations
        for op_name, metrics_list in self.metrics.items():
            if len(metrics_list) > 50:  # Frequently called
                avg_time = sum(m.execution_time for m in metrics_list) / len(metrics_list)
                if avg_time > 1.0:  # Slow
                    opportunities.append(f"Optimize frequently called slow operation: {op_name}")
        
        # Check for operations with high error rates
        for op_name, metrics_list in self.metrics.items():
            if len(metrics_list) > 10:
                error_rate = sum(m.error_count for m in metrics_list) / len(metrics_list)
                if error_rate > 0.1:
                    opportunities.append(f"Improve error handling for: {op_name}")
        
        # Check cache efficiency
        cache_stats = self.cache.stats()
        if cache_stats["size"] > cache_stats["max_size"] * 0.9:
            opportunities.append("Consider increasing cache size")
        
        return opportunities

# Global performance optimizer instance
_performance_optimizer: Optional[PerformanceOptimizer] = None

def get_performance_optimizer() -> PerformanceOptimizer:
    """Get the global performance optimizer instance."""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer

# Convenience decorators
def cached(ttl: int = 3600, key_func: Optional[Callable] = None):
    """Convenience decorator for caching."""
    return get_performance_optimizer().cached(ttl=ttl, key_func=key_func)

def timed(operation_name: Optional[str] = None):
    """Convenience decorator for timing."""
    return get_performance_optimizer().timed(operation_name=operation_name)

def batch_process(items: List[Any], processor: Callable, 
                 batch_size: int = 10, max_workers: int = 4) -> List[Any]:
    """Convenience function for batch processing."""
    return get_performance_optimizer().batch_process(items, processor, batch_size, max_workers)
