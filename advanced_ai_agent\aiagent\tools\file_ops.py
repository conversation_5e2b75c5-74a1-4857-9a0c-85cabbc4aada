"""
Advanced file operations tool with intelligent capabilities.

This tool provides comprehensive file management with smart analysis,
context-aware editing, automated refactoring, and intelligent file operations.
"""

import os
import shutil
import difflib
import logging
from typing import Generator, Optional, List, Dict, Any
from pathlib import Path
import mimetypes
import chardet

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


class FileOperationsTool(ToolSpec):
    """Tool for file operations including read, write, edit, and patch."""
    
    def __init__(self):
        super().__init__(
            name="file",
            description="Read, write, edit, and manage files with intelligent patching",
            parameters=[
                Parameter(
                    name="operation",
                    type="string",
                    description="Operation to perform",
                    required=True,
                    enum=["read", "write", "append", "patch", "delete", "copy", "move", "list", "mkdir", "analyze", "search", "replace", "refactor", "format"]
                ),
                Parameter(
                    name="path",
                    type="string",
                    description="File or directory path",
                    required=True
                ),
                Parameter(
                    name="content",
                    type="string",
                    description="Content for write/append/patch operations",
                    required=False
                ),
                Parameter(
                    name="destination",
                    type="string",
                    description="Destination path for copy/move operations",
                    required=False
                ),
                Parameter(
                    name="encoding",
                    type="string",
                    description="File encoding (default: auto-detect)",
                    required=False,
                    default="auto"
                ),
                Parameter(
                    name="backup",
                    type="boolean",
                    description="Create backup before modifying",
                    required=False,
                    default=True
                )
            ],
            block_types=["file", "read", "write", "patch", "edit"]
        )
    
    def is_available(self) -> bool:
        """Check if file operations are available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute file operation.
        
        Args:
            content: Operation content (can contain operation and path)
            **kwargs: Additional parameters
        
        Yields:
            Response messages with operation results
        """
        try:
            # Parse content if operation not specified in kwargs
            if "operation" not in kwargs:
                operation, path, file_content = self._parse_content(content)
                kwargs.update({
                    "operation": operation,
                    "path": path,
                    "content": file_content
                })
            
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            operation = params["operation"]
            path = params["path"]
            
            # Execute operation
            if operation == "read":
                yield from self._read_file(path, params.get("encoding", "auto"))
            elif operation == "write":
                yield from self._write_file(
                    path, 
                    params.get("content", ""),
                    params.get("encoding", "auto"),
                    params.get("backup", True)
                )
            elif operation == "append":
                yield from self._append_file(
                    path,
                    params.get("content", ""),
                    params.get("encoding", "auto")
                )
            elif operation == "patch":
                yield from self._patch_file(
                    path,
                    params.get("content", ""),
                    params.get("backup", True)
                )
            elif operation == "delete":
                yield from self._delete_file(path)
            elif operation == "copy":
                yield from self._copy_file(path, params.get("destination"))
            elif operation == "move":
                yield from self._move_file(path, params.get("destination"))
            elif operation == "list":
                yield from self._list_directory(path)
            elif operation == "mkdir":
                yield from self._create_directory(path)
            else:
                yield self.create_response(f"Unknown operation: {operation}")
                
        except Exception as e:
            logger.error(f"Error in file operation: {e}")
            yield self.create_response(self.format_error(e))
    
    def _parse_content(self, content: str) -> tuple[str, str, str]:
        """Parse content to extract operation, path, and file content."""
        lines = content.strip().split('\n')
        
        if not lines:
            raise ValueError("No content provided")
        
        first_line = lines[0].strip()
        
        # Try to parse operation and path from first line
        parts = first_line.split(None, 2)
        
        if len(parts) >= 2:
            operation = parts[0].lower()
            path = parts[1]
            
            # Rest is file content
            if len(parts) > 2:
                file_content = parts[2] + '\n' + '\n'.join(lines[1:])
            else:
                file_content = '\n'.join(lines[1:])
        else:
            # Default to read operation
            operation = "read"
            path = first_line
            file_content = ""
        
        return operation, path, file_content
    
    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Read first 10KB
                result = chardet.detect(raw_data)
                return result.get('encoding', 'utf-8') or 'utf-8'
        except Exception:
            return 'utf-8'
    
    def _read_file(self, path: str, encoding: str = "auto") -> Generator[Message, None, None]:
        """Read a file."""
        try:
            file_path = Path(path)
            
            if not file_path.exists():
                yield self.create_response(f"File not found: {path}")
                return
            
            if not file_path.is_file():
                yield self.create_response(f"Path is not a file: {path}")
                return
            
            # Detect encoding if auto
            if encoding == "auto":
                encoding = self._detect_encoding(file_path)
            
            # Check if file is binary
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and not mime_type.startswith('text/'):
                yield self.create_response(
                    f"Binary file detected: {path} (MIME: {mime_type})\n"
                    f"Size: {file_path.stat().st_size} bytes"
                )
                return
            
            # Read file content
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                
                # Add file info
                file_info = f"File: {path}\nSize: {len(content)} characters\nEncoding: {encoding}\n\n"
                
                yield self.create_response(file_info + content)
                
            except UnicodeDecodeError:
                # Try with different encodings
                for fallback_encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        with open(file_path, 'r', encoding=fallback_encoding) as f:
                            content = f.read()
                        
                        file_info = f"File: {path}\nSize: {len(content)} characters\nEncoding: {fallback_encoding} (fallback)\n\n"
                        yield self.create_response(file_info + content)
                        return
                    except UnicodeDecodeError:
                        continue
                
                yield self.create_response(f"Could not decode file: {path}")
                
        except Exception as e:
            yield self.create_response(f"Error reading file: {e}")
    
    def _write_file(
        self, 
        path: str, 
        content: str, 
        encoding: str = "auto",
        backup: bool = True
    ) -> Generator[Message, None, None]:
        """Write content to a file."""
        try:
            file_path = Path(path)
            
            # Create parent directories if needed
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create backup if file exists and backup is requested
            if backup and file_path.exists():
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                shutil.copy2(file_path, backup_path)
                yield self.create_response(f"Created backup: {backup_path}")
            
            # Use UTF-8 if auto encoding
            if encoding == "auto":
                encoding = "utf-8"
            
            # Write file
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            yield self.create_response(
                f"Successfully wrote {len(content)} characters to {path}"
            )
            
        except Exception as e:
            yield self.create_response(f"Error writing file: {e}")
    
    def _append_file(
        self, 
        path: str, 
        content: str, 
        encoding: str = "auto"
    ) -> Generator[Message, None, None]:
        """Append content to a file."""
        try:
            file_path = Path(path)
            
            # Create parent directories if needed
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Use UTF-8 if auto encoding
            if encoding == "auto":
                encoding = "utf-8"
            
            # Append to file
            with open(file_path, 'a', encoding=encoding) as f:
                f.write(content)
            
            yield self.create_response(
                f"Successfully appended {len(content)} characters to {path}"
            )
            
        except Exception as e:
            yield self.create_response(f"Error appending to file: {e}")
    
    def _patch_file(
        self, 
        path: str, 
        patch_content: str, 
        backup: bool = True
    ) -> Generator[Message, None, None]:
        """Apply intelligent patch to a file."""
        try:
            file_path = Path(path)
            
            if not file_path.exists():
                yield self.create_response(f"File not found: {path}")
                return
            
            # Read current content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Apply patch (simple implementation - can be enhanced)
            new_content = self._apply_patch(original_content, patch_content)
            
            if new_content == original_content:
                yield self.create_response("No changes needed")
                return
            
            # Create backup if requested
            if backup:
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                shutil.copy2(file_path, backup_path)
                yield self.create_response(f"Created backup: {backup_path}")
            
            # Write patched content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            # Show diff
            diff = list(difflib.unified_diff(
                original_content.splitlines(keepends=True),
                new_content.splitlines(keepends=True),
                fromfile=f"{path} (original)",
                tofile=f"{path} (patched)"
            ))
            
            if diff:
                yield self.create_response(f"Applied patch to {path}:\n\n{''.join(diff)}")
            else:
                yield self.create_response(f"Successfully patched {path}")
                
        except Exception as e:
            yield self.create_response(f"Error patching file: {e}")
    
    def _apply_patch(self, original: str, patch: str) -> str:
        """Apply a simple patch (replace content between markers or full replace)."""
        # Simple implementation - can be enhanced with proper diff/patch logic
        return patch
    
    def _delete_file(self, path: str) -> Generator[Message, None, None]:
        """Delete a file or directory."""
        try:
            file_path = Path(path)
            
            if not file_path.exists():
                yield self.create_response(f"Path not found: {path}")
                return
            
            if file_path.is_file():
                file_path.unlink()
                yield self.create_response(f"Deleted file: {path}")
            elif file_path.is_dir():
                shutil.rmtree(file_path)
                yield self.create_response(f"Deleted directory: {path}")
            else:
                yield self.create_response(f"Unknown path type: {path}")
                
        except Exception as e:
            yield self.create_response(f"Error deleting: {e}")
    
    def _copy_file(self, source: str, destination: str) -> Generator[Message, None, None]:
        """Copy a file or directory."""
        try:
            if not destination:
                yield self.create_response("Destination path required for copy operation")
                return
            
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                yield self.create_response(f"Source not found: {source}")
                return
            
            if source_path.is_file():
                shutil.copy2(source_path, dest_path)
                yield self.create_response(f"Copied file: {source} -> {destination}")
            elif source_path.is_dir():
                shutil.copytree(source_path, dest_path)
                yield self.create_response(f"Copied directory: {source} -> {destination}")
                
        except Exception as e:
            yield self.create_response(f"Error copying: {e}")
    
    def _move_file(self, source: str, destination: str) -> Generator[Message, None, None]:
        """Move a file or directory."""
        try:
            if not destination:
                yield self.create_response("Destination path required for move operation")
                return
            
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                yield self.create_response(f"Source not found: {source}")
                return
            
            shutil.move(str(source_path), str(dest_path))
            yield self.create_response(f"Moved: {source} -> {destination}")
            
        except Exception as e:
            yield self.create_response(f"Error moving: {e}")
    
    def _list_directory(self, path: str) -> Generator[Message, None, None]:
        """List directory contents."""
        try:
            dir_path = Path(path)
            
            if not dir_path.exists():
                yield self.create_response(f"Directory not found: {path}")
                return
            
            if not dir_path.is_dir():
                yield self.create_response(f"Path is not a directory: {path}")
                return
            
            items = []
            for item in sorted(dir_path.iterdir()):
                if item.is_dir():
                    items.append(f"📁 {item.name}/")
                else:
                    size = item.stat().st_size
                    items.append(f"📄 {item.name} ({size} bytes)")
            
            if items:
                yield self.create_response(f"Contents of {path}:\n\n" + "\n".join(items))
            else:
                yield self.create_response(f"Directory is empty: {path}")
                
        except Exception as e:
            yield self.create_response(f"Error listing directory: {e}")
    
    def _create_directory(self, path: str) -> Generator[Message, None, None]:
        """Create a directory."""
        try:
            dir_path = Path(path)
            dir_path.mkdir(parents=True, exist_ok=True)
            yield self.create_response(f"Created directory: {path}")
            
        except Exception as e:
            yield self.create_response(f"Error creating directory: {e}")


# Create tool instance
file_ops_tool = FileOperationsTool()
