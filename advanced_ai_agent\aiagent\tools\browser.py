"""
Browser tool for web browsing and automation.

This tool provides web browsing capabilities with intelligent
web interaction, scraping, and automation features.
"""

import logging
import re
import json
from typing import Generator, Optional, Dict, Any
from urllib.parse import urlparse, urljoin
from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)

class BrowserTool(ToolSpec):
    """Advanced browser tool for web automation and scraping."""

    def __init__(self):
        super().__init__(
            name="browser",
            description="Browse the web, scrape content, and interact with web pages",
            parameters=[
                Parameter("action", "str", "Action to perform: navigate, scrape, search, click, type", required=True),
                Parameter("url", "str", "URL to navigate to", required=False),
                Parameter("query", "str", "Search query or text to type", required=False),
                Parameter("selector", "str", "CSS selector for element interaction", required=False),
                Parameter("wait_for", "str", "Element to wait for before proceeding", required=False)
            ],
            block_types=["browser", "web", "url", "scrape"]
        )
        self._session = None

    def is_available(self) -> bool:
        """Check if browser tool is available."""
        try:
            import requests
            return True
        except ImportError:
            return False

    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute browser operations."""
        try:
            # Parse the content to extract action and parameters
            action_info = self._parse_browser_command(content)
            action = action_info.get("action", "navigate")

            if action == "navigate":
                yield from self._navigate(action_info)
            elif action == "scrape":
                yield from self._scrape_content(action_info)
            elif action == "search":
                yield from self._web_search(action_info)
            elif action == "download":
                yield from self._download_file(action_info)
            else:
                yield self.create_response(f"Unknown browser action: {action}")

        except Exception as e:
            yield self.create_response(f"Browser error: {str(e)}")

    def _parse_browser_command(self, content: str) -> Dict[str, Any]:
        """Parse browser command from content."""
        lines = content.strip().split('\n')
        action_info = {"action": "navigate"}

        for line in lines:
            line = line.strip()
            if line.startswith('action:'):
                action_info["action"] = line.split(':', 1)[1].strip()
            elif line.startswith('url:'):
                action_info["url"] = line.split(':', 1)[1].strip()
            elif line.startswith('query:'):
                action_info["query"] = line.split(':', 1)[1].strip()
            elif line.startswith('selector:'):
                action_info["selector"] = line.split(':', 1)[1].strip()
            elif not line.startswith(('action:', 'url:', 'query:', 'selector:')) and line:
                # Treat as URL if it looks like one, otherwise as query
                if self._is_url(line):
                    action_info["url"] = line
                else:
                    action_info["query"] = line

        return action_info

    def _is_url(self, text: str) -> bool:
        """Check if text looks like a URL."""
        try:
            result = urlparse(text)
            return all([result.scheme, result.netloc])
        except:
            return False

    def _navigate(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Navigate to a URL and return page information."""
        url = action_info.get("url", "")
        if not url:
            yield self.create_response("Error: No URL provided for navigation")
            return

        try:
            import requests
            from bs4 import BeautifulSoup

            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract page information
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"

            # Extract main content
            content_text = ""
            for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                text = tag.get_text().strip()
                if text:
                    content_text += text + "\n"

            # Limit content length
            if len(content_text) > 2000:
                content_text = content_text[:2000] + "... [truncated]"

            result = f"Successfully navigated to: {url}\n"
            result += f"Title: {title_text}\n"
            result += f"Status: {response.status_code}\n\n"
            result += f"Content:\n{content_text}"

            yield self.create_response(result)

        except ImportError:
            yield self.create_response("Error: requests and beautifulsoup4 libraries required for browser functionality")
        except Exception as e:
            yield self.create_response(f"Navigation error: {str(e)}")

    def _scrape_content(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Scrape content from a webpage."""
        url = action_info.get("url", "")
        selector = action_info.get("selector", "")

        if not url:
            yield self.create_response("Error: No URL provided for scraping")
            return

        try:
            import requests
            from bs4 import BeautifulSoup

            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            if selector:
                # Use CSS selector if provided
                elements = soup.select(selector)
                if elements:
                    content = "\n".join([elem.get_text().strip() for elem in elements[:10]])
                else:
                    content = f"No elements found with selector: {selector}"
            else:
                # Extract all text content
                content = soup.get_text()
                # Clean up whitespace
                content = re.sub(r'\s+', ' ', content).strip()

            # Limit content length
            if len(content) > 3000:
                content = content[:3000] + "... [truncated]"

            result = f"Scraped content from: {url}\n"
            if selector:
                result += f"Using selector: {selector}\n"
            result += f"\nContent:\n{content}"

            yield self.create_response(result)

        except ImportError:
            yield self.create_response("Error: requests and beautifulsoup4 libraries required")
        except Exception as e:
            yield self.create_response(f"Scraping error: {str(e)}")

    def _web_search(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform web search using DuckDuckGo."""
        query = action_info.get("query", "")
        if not query:
            yield self.create_response("Error: No search query provided")
            return

        try:
            import requests
            from urllib.parse import quote

            # Use DuckDuckGo Instant Answer API
            search_url = f"https://api.duckduckgo.com/?q={quote(query)}&format=json&no_html=1&skip_disambig=1"

            response = requests.get(search_url, timeout=10)
            response.raise_for_status()

            data = response.json()

            result = f"Search results for: {query}\n\n"

            # Abstract
            if data.get('Abstract'):
                result += f"Summary: {data['Abstract']}\n"
                if data.get('AbstractURL'):
                    result += f"Source: {data['AbstractURL']}\n\n"

            # Related topics
            if data.get('RelatedTopics'):
                result += "Related topics:\n"
                for topic in data['RelatedTopics'][:5]:
                    if isinstance(topic, dict) and topic.get('Text'):
                        result += f"- {topic['Text']}\n"
                        if topic.get('FirstURL'):
                            result += f"  URL: {topic['FirstURL']}\n"

            # Answer
            if data.get('Answer'):
                result += f"\nDirect Answer: {data['Answer']}\n"

            if not any([data.get('Abstract'), data.get('RelatedTopics'), data.get('Answer')]):
                result += "No specific results found. Try a more specific query."

            yield self.create_response(result)

        except ImportError:
            yield self.create_response("Error: requests library required for web search")
        except Exception as e:
            yield self.create_response(f"Search error: {str(e)}")

    def _download_file(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Download a file from URL."""
        url = action_info.get("url", "")
        if not url:
            yield self.create_response("Error: No URL provided for download")
            return

        try:
            import requests
            import os
            from urllib.parse import urlparse

            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Get filename from URL or Content-Disposition header
            filename = None
            if 'content-disposition' in response.headers:
                import re
                cd = response.headers['content-disposition']
                filename_match = re.findall('filename=(.+)', cd)
                if filename_match:
                    filename = filename_match[0].strip('"')

            if not filename:
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path) or "downloaded_file"

            # Save file
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size = os.path.getsize(filename)
            result = f"Successfully downloaded: {filename}\n"
            result += f"Size: {file_size} bytes\n"
            result += f"From: {url}"

            yield self.create_response(result)

        except ImportError:
            yield self.create_response("Error: requests library required for file download")
        except Exception as e:
            yield self.create_response(f"Download error: {str(e)}")

# Create tool instance
browser_tool = BrowserTool()
