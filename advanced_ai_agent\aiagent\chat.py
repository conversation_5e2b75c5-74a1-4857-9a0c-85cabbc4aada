"""
Advanced chat system with workflow integration.

This module provides intelligent conversation management with workflow tracking,
context awareness, predictive suggestions, and step-by-step execution capabilities.
"""

import logging
import os
import sys
import time
from typing import List, Optional, Generator, Callable
from pathlib import Path

from message import Message, create_user_message, create_system_message
from llm import reply, get_provider
from tools import init_tools, execute_msg, get_tools_for_llm, ToolFormat
from config import get_config
from conversation import ConversationManager
from workflow import get_workflow_engine, WorkflowStep, WorkflowPriority
from utils.prompt import get_user_input, create_system_prompt
from utils.console import console, print_message
from utils.interrupt import handle_interrupt, set_interruptible, clear_interruptible
from utils.token_manager import get_token_manager, TokenLimitStrategy
from utils.performance import get_performance_optimizer, timed, cached
from reasoning import get_intelligence_engine, ReasoningType

logger = logging.getLogger(__name__)

# Type alias for confirmation function
ConfirmFunc = Callable[[str], bool]


def chat(
    initial_messages: List[Message],
    workspace: Optional[Path] = None,
    model: Optional[str] = None,
    stream: bool = False,  # Default to instant rendering (no streaming)
    interactive: bool = True,
    auto_confirm: bool = False,
    tool_allowlist: Optional[List[str]] = None,
    conversation_id: Optional[str] = None
) -> None:
    """
    Main chat function for interactive AI agent sessions with instant text rendering.

    Args:
        initial_messages: Initial conversation messages
        workspace: Working directory path
        model: LLM model to use
        stream: Whether to stream responses (disabled by default for instant rendering)
        interactive: Whether to run in interactive mode
        auto_confirm: Whether to auto-confirm tool executions
        tool_allowlist: List of allowed tools
        conversation_id: Conversation ID for persistence
    """
    config = get_config()
    
    # Set workspace
    if workspace:
        os.chdir(workspace)
        console.print(f"Working directory: {workspace}")
    
    # Initialize tools
    tools = init_tools(tool_allowlist)
    console.print(f"Initialized {len(tools)} tools: {[t.name for t in tools]}")
    
    # Initialize conversation manager
    conversation = ConversationManager(conversation_id)
    
    # Add initial messages
    for msg in initial_messages:
        conversation.add_message(msg)
    
    # Create confirmation function
    def confirm_func(message: str) -> bool:
        if auto_confirm:
            return True
        
        response = input(f"\n{message} (y/N): ").strip().lower()
        return response in ['y', 'yes']
    
    # Print conversation history
    if conversation.messages:
        console.print("\n--- Conversation History ---")
        for msg in conversation.messages:
            print_message(msg)
        console.print("--- End History ---\n")
    
    # Main chat loop
    iteration_count = 0
    max_iterations = 50  # Prevent infinite loops while allowing task completion
    task_completion_indicators = [
        "task completed", "task finished", "task done", "completed successfully",
        "finished successfully", "all done", "task complete", "work completed",
        "implementation complete", "solution complete", "finished the task"
    ]

    try:
        while iteration_count < max_iterations:
            iteration_count += 1

            try:
                # Get user input (if interactive)
                if interactive:
                    user_input = get_user_input()
                    if user_input is None:  # User wants to exit
                        break

                    # Handle special commands
                    if user_input.startswith('/'):
                        if handle_command(user_input, conversation):
                            continue
                        else:
                            break

                    # Add user message
                    user_msg = create_user_message(user_input)
                    conversation.add_message(user_msg)
                    print_message(user_msg)

                # Generate AI response
                response_generator = generate_response(
                    conversation.messages,
                    model=model,
                    stream=stream,
                    tools=get_tools_for_llm()
                )

                # Process response and accumulate content
                assistant_msg = None
                accumulated_content = ""
                final_message = None

                for msg_chunk in response_generator:
                    if stream:
                        print_message(msg_chunk, end="")
                        if hasattr(msg_chunk, 'content'):
                            accumulated_content += msg_chunk.content
                        # Check if this is the final message
                        if msg_chunk.metadata.get("final"):
                            final_message = msg_chunk
                    else:
                        assistant_msg = msg_chunk

                # Create the final assistant message
                if stream and accumulated_content:
                    assistant_msg = Message(
                        role="assistant",
                        content=accumulated_content,
                        metadata={"model": model, "iteration": iteration_count}
                    )
                elif stream and final_message:
                    assistant_msg = final_message

                if assistant_msg:
                    conversation.add_message(assistant_msg)
                    if not stream:
                        print_message(assistant_msg)

                # Execute any tools in the response with enhanced processing
                tool_executed = False
                tool_results = []
                if assistant_msg:
                    tool_responses = list(execute_msg(assistant_msg, confirm_func))
                    for tool_response in tool_responses:
                        conversation.add_message(tool_response)
                        print_message(tool_response)
                        tool_executed = True
                        tool_results.append(tool_response)

                        # Check if tool execution suggests more work is needed
                        if tool_response.metadata.get("error"):
                            # Tool error - might need retry or different approach
                            continue
                        elif tool_response.metadata.get("success", True):
                            # Successful tool execution
                            tool_name = tool_response.metadata.get("tool", "unknown")
                            # Add context for next iteration
                            context_msg = create_system_message(
                                f"Tool {tool_name} executed successfully. Continue with next steps if needed.",
                                metadata={"context": True, "tool_success": tool_name}
                            )
                            conversation.add_message(context_msg)

                # Save conversation
                conversation.save()

                # Check for task completion in non-interactive mode
                if not interactive:
                    # Check if the assistant indicates task completion
                    if assistant_msg and assistant_msg.content:
                        content_lower = assistant_msg.content.lower()
                        task_seems_complete = any(indicator in content_lower for indicator in task_completion_indicators)

                        # Also check if no tools were executed and response seems conclusive
                        no_more_work = not tool_executed and (
                            "no further" in content_lower or
                            "nothing more" in content_lower or
                            "that's all" in content_lower or
                            "implementation is complete" in content_lower or
                            "all requirements" in content_lower
                        )

                        # Exit conditions for non-interactive mode
                        if task_seems_complete or no_more_work:
                            break

                        # Continue if tools were executed or response suggests more work
                        if tool_executed or any(word in content_lower for word in [
                            "next", "now", "then", "continue", "proceed", "let me", "i'll", "i will"
                        ]):
                            continue

                    # If we've done several iterations without clear progress, break
                    if iteration_count >= 10 and not tool_executed:
                        break

            except KeyboardInterrupt:
                console.print("\n[yellow]Interrupted by user[/yellow]")
                if not interactive:
                    break
                continue
            except Exception as e:
                logger.error(f"Error in chat loop: {e}")
                console.print(f"[red]Error: {e}[/red]")
                if not interactive:
                    # Don't break on errors in non-interactive mode, try to continue
                    continue
                continue
    
    finally:
        # Save final conversation state
        conversation.save()
        console.print("\n[green]Conversation saved[/green]")


@timed("generate_response")
@cached(ttl=300, key_func=lambda messages, model, stream, tools: f"{model}_{len(messages)}_{hash(str(messages[-1].content if messages else ''))}")
def generate_response(
    messages: List[Message],
    model: Optional[str] = None,
    stream: bool = True,
    tools: Optional[List[dict]] = None
) -> Generator[Message, None, None]:
    """
    Generate AI response for given messages with enhanced intelligence and performance optimization.

    Args:
        messages: Conversation messages
        model: Model to use
        stream: Whether to stream response
        tools: Available tools

    Yields:
        Response message chunks
    """
    try:
        set_interruptible()

        # Get intelligence engine and token manager for optimization
        intelligence = get_intelligence_engine()
        token_manager = get_token_manager()

        # Estimate token usage for the request
        total_context = "\n".join(str(msg.content) if hasattr(msg, 'content') else str(msg) for msg in messages)
        estimated_input_tokens = token_manager.estimate_tokens(total_context)

        # Check token limits before proceeding
        can_proceed, limit_reason = token_manager.check_token_limits(estimated_input_tokens, model or "gemini-2.0-flash-exp")

        if not can_proceed:
            yield Message(
                role="system",
                content=f"Token limit exceeded: {limit_reason}. Optimizing context...",
                metadata={"token_limit_hit": True}
            )

            # Optimize context to fit within limits
            optimized_messages = token_manager.optimize_context(
                messages,
                token_manager.budget.max_tokens_per_request // 2,  # Leave room for response
                TokenLimitStrategy.SMART_COMPRESSION
            )

            # Recalculate with optimized context
            optimized_context = "\n".join(str(msg.content) if hasattr(msg, 'content') else str(msg) for msg in optimized_messages)
            estimated_input_tokens = token_manager.estimate_tokens(optimized_context)
            messages = optimized_messages

        # Analyze the current situation
        context = messages[-1].content if messages else "General conversation"
        available_data = {
            "message_count": len(messages),
            "tools_available": len(tools) if tools else 0,
            "conversation_context": context,
            "estimated_tokens": estimated_input_tokens,
            "token_usage_stats": token_manager.get_usage_stats()
        }

        # Perform intelligent analysis
        analysis = intelligence.analyze_situation(context, available_data)

        # Make intelligent decisions about response approach
        response_options = [
            {
                "name": "direct_response",
                "description": "Provide direct answer without tools",
                "expected_outcome": "Quick response",
                "complexity": 0.3
            },
            {
                "name": "tool_assisted_response",
                "description": "Use tools to enhance response",
                "expected_outcome": "Comprehensive solution",
                "complexity": 0.7
            },
            {
                "name": "multi_step_reasoning",
                "description": "Break down into steps with reasoning",
                "expected_outcome": "Thorough analysis and solution",
                "complexity": 0.9
            }
        ]

        # Let intelligence engine decide the best approach
        decision = intelligence.make_decision(
            context=f"Responding to: {context}",
            options=response_options,
            constraints={"tools_available": tools is not None}
        )

        # Enhance the system prompt based on intelligent decision
        enhanced_messages = messages.copy()

        # Add intelligence-driven enhancement to the last user message
        if enhanced_messages and enhanced_messages[-1].role == "user":
            last_msg = enhanced_messages[-1]

            # Create intelligent enhancement based on decision
            intelligence_prompt = f"""

ADVANCED AI AGENT - INTELLIGENT RESPONSE MODE:
- Analysis Confidence: {analysis['confidence']:.2%}
- Chosen Approach: {decision.chosen_option['name']}
- Expected Outcome: {decision.chosen_option['expected_outcome']}
- Decision Confidence: {decision.confidence:.2%}

INTELLIGENT INSTRUCTIONS:
- You are an advanced AI agent with sophisticated reasoning capabilities
- Use the chosen approach: {decision.chosen_option['description']}
- Apply step-by-step reasoning and decision-making
- Use ANY tool you need to complete the task fully and comprehensively
- Continue working until the entire task is completely finished
- Be autonomous, proactive, and thorough in your approach
- Learn from each interaction to improve future responses
- Only indicate completion when the task is truly 100% finished

REASONING INSIGHTS:
{chr(10).join(f"- {insight}" for insight in analysis['insights'])}
"""
            enhanced_messages[-1] = last_msg.replace(content=last_msg.content + intelligence_prompt)

        # Generate response using LLM with INSTANT RENDERING (no streaming)
        # Always disable streaming for instant text display
        response = reply(
            messages=enhanced_messages,
            model=model,
            stream=False,  # Force instant rendering
            tools=tools
        )

        # Process response for instant display and track token usage
        response_content = ""
        final_metadata = {}

        if hasattr(response, '__iter__') and not hasattr(response, 'content'):
            # If response is iterable but not a single message, accumulate all content
            for chunk in response:
                if hasattr(chunk, 'content') and chunk.content:
                    response_content += chunk.content
                if hasattr(chunk, 'metadata') and chunk.metadata:
                    final_metadata.update(chunk.metadata)
        else:
            # Single response
            if hasattr(response, 'content'):
                response_content = response.content
                final_metadata = response.metadata if hasattr(response, 'metadata') and response.metadata else {}
            else:
                response_content = str(response)

        # Track token usage
        if response_content:
            estimated_output_tokens = token_manager.estimate_tokens(response_content)
            request_id = f"req_{int(time.time() * 1000)}"

            usage = token_manager.track_usage(
                request_id=request_id,
                input_tokens=estimated_input_tokens,
                output_tokens=estimated_output_tokens,
                model=model or "gemini-2.0-flash-exp",
                context_length=len(total_context)
            )

            # Add token usage to metadata
            final_metadata.update({
                "instant_render": True,
                "model": model,
                "token_usage": {
                    "input_tokens": usage.input_tokens,
                    "output_tokens": usage.output_tokens,
                    "total_tokens": usage.total_tokens,
                    "cost_estimate": usage.cost_estimate
                },
                "optimization_suggestions": token_manager.suggest_optimizations()
            })

            # Yield the complete response
            yield Message(
                role="assistant",
                content=response_content,
                metadata=final_metadata
            )
        else:
            # Fallback for empty response
            yield Message(
                role="assistant",
                content="I apologize, but I couldn't generate a response. Please try again.",
                metadata={
                    "model": model,
                    "instant_render": True,
                    "response_type": "fallback",
                    "error": "empty_response"
                }
            )

    except KeyboardInterrupt:
        yield create_system_message("Response generation interrupted")
    except Exception as e:
        # Enhanced error handling with performance tracking
        error_type = type(e).__name__
        error_message = str(e)

        # Track error for performance analysis
        performance_optimizer = get_performance_optimizer()
        performance_optimizer.error_patterns[f"generate_response_{error_type}"] += 1

        # Provide helpful error message based on error type
        if "token" in error_message.lower():
            user_message = "Token limit exceeded. Try breaking your request into smaller parts."
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            user_message = "Network connection issue. Please check your internet connection and try again."
        elif "api" in error_message.lower():
            user_message = "API service temporarily unavailable. Please try again in a moment."
        else:
            user_message = f"I encountered an error: {error_message}. Please try again."

        logger.error(f"Error generating response: {e}")
        yield create_system_message(user_message)
    finally:
        clear_interruptible()


def handle_command(command: str, conversation: ConversationManager) -> bool:
    """
    Handle special chat commands.
    
    Args:
        command: Command string starting with '/'
        conversation: Conversation manager
    
    Returns:
        True to continue chat, False to exit
    """
    command = command.lower().strip()
    
    if command in ['/exit', '/quit', '/q']:
        return False
    
    elif command == '/help':
        console.print("""
Available commands:
  /help     - Show this help message
  /exit     - Exit the chat
  /clear    - Clear conversation history
  /save     - Save conversation
  /load     - Load conversation
  /tools    - List available tools
  /model    - Show current model info
  /history  - Show conversation history
        """)
    
    elif command == '/clear':
        conversation.clear()
        console.print("[yellow]Conversation history cleared[/yellow]")
    
    elif command == '/save':
        conversation.save()
        console.print("[green]Conversation saved[/green]")
    
    elif command == '/tools':
        tools = init_tools()
        console.print(f"Available tools ({len(tools)}):")
        for tool in tools:
            status = "✓" if tool.is_available() else "✗"
            console.print(f"  {status} {tool.name}: {tool.description}")
    
    elif command == '/model':
        provider = get_provider()
        console.print(f"Current model: {provider.model}")
    
    elif command == '/history':
        console.print("\n--- Conversation History ---")
        for i, msg in enumerate(conversation.messages):
            console.print(f"{i+1}. {msg.role}: {msg.content[:100]}...")
        console.print("--- End History ---\n")
    
    else:
        console.print(f"[red]Unknown command: {command}[/red]")
    
    return True


def create_initial_messages(
    system_prompt: Optional[str] = None,
    tools: Optional[List[str]] = None
) -> List[Message]:
    """
    Create initial system messages for a conversation.
    
    Args:
        system_prompt: Custom system prompt
        tools: List of available tools
    
    Returns:
        List of initial messages
    """
    messages = []
    
    # Create system prompt
    if system_prompt is None:
        system_prompt = create_system_prompt(tools or [])
    
    messages.append(create_system_message(system_prompt))
    
    return messages


__all__ = [
    "chat",
    "generate_response", 
    "handle_command",
    "create_initial_messages",
    "ConfirmFunc",
]
