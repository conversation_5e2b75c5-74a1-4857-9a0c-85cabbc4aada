"""
Advanced chat system with workflow integration.

This module provides intelligent conversation management with workflow tracking,
context awareness, predictive suggestions, and step-by-step execution capabilities.
"""

import logging
import os
import sys
from typing import List, Optional, Generator, Callable
from pathlib import Path

from message import Message, create_user_message, create_system_message
from llm import reply, get_provider
from tools import init_tools, execute_msg, get_tools_for_llm, ToolFormat
from config import get_config
from conversation import ConversationManager
from workflow import get_workflow_engine, WorkflowStep, WorkflowPriority
from utils.prompt import get_user_input, create_system_prompt
from utils.console import console, print_message
from utils.interrupt import handle_interrupt, set_interruptible, clear_interruptible

logger = logging.getLogger(__name__)

# Type alias for confirmation function
ConfirmFunc = Callable[[str], bool]


def chat(
    initial_messages: List[Message],
    workspace: Optional[Path] = None,
    model: Optional[str] = None,
    stream: bool = True,
    interactive: bool = True,
    auto_confirm: bool = False,
    tool_allowlist: Optional[List[str]] = None,
    conversation_id: Optional[str] = None
) -> None:
    """
    Main chat function for interactive AI agent sessions.
    
    Args:
        initial_messages: Initial conversation messages
        workspace: Working directory path
        model: LLM model to use
        stream: Whether to stream responses
        interactive: Whether to run in interactive mode
        auto_confirm: Whether to auto-confirm tool executions
        tool_allowlist: List of allowed tools
        conversation_id: Conversation ID for persistence
    """
    config = get_config()
    
    # Set workspace
    if workspace:
        os.chdir(workspace)
        console.print(f"Working directory: {workspace}")
    
    # Initialize tools
    tools = init_tools(tool_allowlist)
    console.print(f"Initialized {len(tools)} tools: {[t.name for t in tools]}")
    
    # Initialize conversation manager
    conversation = ConversationManager(conversation_id)
    
    # Add initial messages
    for msg in initial_messages:
        conversation.add_message(msg)
    
    # Create confirmation function
    def confirm_func(message: str) -> bool:
        if auto_confirm:
            return True
        
        response = input(f"\n{message} (y/N): ").strip().lower()
        return response in ['y', 'yes']
    
    # Print conversation history
    if conversation.messages:
        console.print("\n--- Conversation History ---")
        for msg in conversation.messages:
            print_message(msg)
        console.print("--- End History ---\n")
    
    # Main chat loop
    iteration_count = 0
    max_iterations = 50  # Prevent infinite loops while allowing task completion
    task_completion_indicators = [
        "task completed", "task finished", "task done", "completed successfully",
        "finished successfully", "all done", "task complete", "work completed",
        "implementation complete", "solution complete", "finished the task"
    ]

    try:
        while iteration_count < max_iterations:
            iteration_count += 1

            try:
                # Get user input (if interactive)
                if interactive:
                    user_input = get_user_input()
                    if user_input is None:  # User wants to exit
                        break

                    # Handle special commands
                    if user_input.startswith('/'):
                        if handle_command(user_input, conversation):
                            continue
                        else:
                            break

                    # Add user message
                    user_msg = create_user_message(user_input)
                    conversation.add_message(user_msg)
                    print_message(user_msg)

                # Generate AI response
                response_generator = generate_response(
                    conversation.messages,
                    model=model,
                    stream=stream,
                    tools=get_tools_for_llm()
                )

                # Process response and accumulate content
                assistant_msg = None
                accumulated_content = ""
                final_message = None

                for msg_chunk in response_generator:
                    if stream:
                        print_message(msg_chunk, end="")
                        if hasattr(msg_chunk, 'content'):
                            accumulated_content += msg_chunk.content
                        # Check if this is the final message
                        if msg_chunk.metadata.get("final"):
                            final_message = msg_chunk
                    else:
                        assistant_msg = msg_chunk

                # Create the final assistant message
                if stream and accumulated_content:
                    assistant_msg = Message(
                        role="assistant",
                        content=accumulated_content,
                        metadata={"model": model, "iteration": iteration_count}
                    )
                elif stream and final_message:
                    assistant_msg = final_message

                if assistant_msg:
                    conversation.add_message(assistant_msg)
                    if not stream:
                        print_message(assistant_msg)

                # Execute any tools in the response with enhanced processing
                tool_executed = False
                tool_results = []
                if assistant_msg:
                    tool_responses = list(execute_msg(assistant_msg, confirm_func))
                    for tool_response in tool_responses:
                        conversation.add_message(tool_response)
                        print_message(tool_response)
                        tool_executed = True
                        tool_results.append(tool_response)

                        # Check if tool execution suggests more work is needed
                        if tool_response.metadata.get("error"):
                            # Tool error - might need retry or different approach
                            continue
                        elif tool_response.metadata.get("success", True):
                            # Successful tool execution
                            tool_name = tool_response.metadata.get("tool", "unknown")
                            # Add context for next iteration
                            context_msg = create_system_message(
                                f"Tool {tool_name} executed successfully. Continue with next steps if needed.",
                                metadata={"context": True, "tool_success": tool_name}
                            )
                            conversation.add_message(context_msg)

                # Save conversation
                conversation.save()

                # Check for task completion in non-interactive mode
                if not interactive:
                    # Check if the assistant indicates task completion
                    if assistant_msg and assistant_msg.content:
                        content_lower = assistant_msg.content.lower()
                        task_seems_complete = any(indicator in content_lower for indicator in task_completion_indicators)

                        # Also check if no tools were executed and response seems conclusive
                        no_more_work = not tool_executed and (
                            "no further" in content_lower or
                            "nothing more" in content_lower or
                            "that's all" in content_lower or
                            "implementation is complete" in content_lower or
                            "all requirements" in content_lower
                        )

                        # Exit conditions for non-interactive mode
                        if task_seems_complete or no_more_work:
                            break

                        # Continue if tools were executed or response suggests more work
                        if tool_executed or any(word in content_lower for word in [
                            "next", "now", "then", "continue", "proceed", "let me", "i'll", "i will"
                        ]):
                            continue

                    # If we've done several iterations without clear progress, break
                    if iteration_count >= 10 and not tool_executed:
                        break

            except KeyboardInterrupt:
                console.print("\n[yellow]Interrupted by user[/yellow]")
                if not interactive:
                    break
                continue
            except Exception as e:
                logger.error(f"Error in chat loop: {e}")
                console.print(f"[red]Error: {e}[/red]")
                if not interactive:
                    # Don't break on errors in non-interactive mode, try to continue
                    continue
                continue
    
    finally:
        # Save final conversation state
        conversation.save()
        console.print("\n[green]Conversation saved[/green]")


def generate_response(
    messages: List[Message],
    model: Optional[str] = None,
    stream: bool = True,
    tools: Optional[List[dict]] = None
) -> Generator[Message, None, None]:
    """
    Generate AI response for given messages with enhanced intelligence.

    Args:
        messages: Conversation messages
        model: Model to use
        stream: Whether to stream response
        tools: Available tools

    Yields:
        Response message chunks
    """
    try:
        set_interruptible()

        # Enhance the system prompt for better task completion
        enhanced_messages = messages.copy()

        # Add intelligence enhancement to the last user message if it exists
        if enhanced_messages and enhanced_messages[-1].role == "user":
            last_msg = enhanced_messages[-1]
            enhanced_content = last_msg.content + """

IMPORTANT INSTRUCTIONS FOR AI AGENT:
- You are an advanced AI agent with access to powerful tools
- Use ANY tool you need to complete the task fully and comprehensively
- Continue working until the entire task is completely finished
- Don't ask for permission - use tools intelligently and autonomously
- If you need to use multiple tools in sequence, do so
- Only indicate completion when the task is truly 100% finished
- Be proactive and thorough in your approach
- Think step by step and execute each step completely
"""
            enhanced_messages[-1] = last_msg.replace(content=enhanced_content)

        # Generate response using LLM
        response = reply(
            messages=enhanced_messages,
            model=model,
            stream=stream,
            tools=tools
        )

        if stream and hasattr(response, '__iter__'):
            # Stream response with better accumulation
            accumulated_content = ""
            chunk_count = 0

            for chunk in response:
                chunk_count += 1
                if hasattr(chunk, 'content') and chunk.content:
                    accumulated_content += chunk.content
                    # Yield chunk with accumulated metadata
                    yield chunk.replace(metadata={
                        **chunk.metadata,
                        "chunk_number": chunk_count,
                        "accumulated_length": len(accumulated_content)
                    })

            # Yield final complete message with all content
            if accumulated_content:
                yield Message(
                    role="assistant",
                    content=accumulated_content,
                    metadata={
                        "final": True,
                        "total_chunks": chunk_count,
                        "total_length": len(accumulated_content),
                        "model": model
                    }
                )
        else:
            # Single response
            if hasattr(response, 'content'):
                yield response
            else:
                # Handle case where response might not have expected structure
                yield Message(
                    role="assistant",
                    content=str(response),
                    metadata={"model": model, "single_response": True}
                )

    except KeyboardInterrupt:
        yield create_system_message("Response generation interrupted")
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        yield create_system_message(f"Error generating response: {e}")
    finally:
        clear_interruptible()


def handle_command(command: str, conversation: ConversationManager) -> bool:
    """
    Handle special chat commands.
    
    Args:
        command: Command string starting with '/'
        conversation: Conversation manager
    
    Returns:
        True to continue chat, False to exit
    """
    command = command.lower().strip()
    
    if command in ['/exit', '/quit', '/q']:
        return False
    
    elif command == '/help':
        console.print("""
Available commands:
  /help     - Show this help message
  /exit     - Exit the chat
  /clear    - Clear conversation history
  /save     - Save conversation
  /load     - Load conversation
  /tools    - List available tools
  /model    - Show current model info
  /history  - Show conversation history
        """)
    
    elif command == '/clear':
        conversation.clear()
        console.print("[yellow]Conversation history cleared[/yellow]")
    
    elif command == '/save':
        conversation.save()
        console.print("[green]Conversation saved[/green]")
    
    elif command == '/tools':
        tools = init_tools()
        console.print(f"Available tools ({len(tools)}):")
        for tool in tools:
            status = "✓" if tool.is_available() else "✗"
            console.print(f"  {status} {tool.name}: {tool.description}")
    
    elif command == '/model':
        provider = get_provider()
        console.print(f"Current model: {provider.model}")
    
    elif command == '/history':
        console.print("\n--- Conversation History ---")
        for i, msg in enumerate(conversation.messages):
            console.print(f"{i+1}. {msg.role}: {msg.content[:100]}...")
        console.print("--- End History ---\n")
    
    else:
        console.print(f"[red]Unknown command: {command}[/red]")
    
    return True


def create_initial_messages(
    system_prompt: Optional[str] = None,
    tools: Optional[List[str]] = None
) -> List[Message]:
    """
    Create initial system messages for a conversation.
    
    Args:
        system_prompt: Custom system prompt
        tools: List of available tools
    
    Returns:
        List of initial messages
    """
    messages = []
    
    # Create system prompt
    if system_prompt is None:
        system_prompt = create_system_prompt(tools or [])
    
    messages.append(create_system_message(system_prompt))
    
    return messages


__all__ = [
    "chat",
    "generate_response", 
    "handle_command",
    "create_initial_messages",
    "ConfirmFunc",
]
