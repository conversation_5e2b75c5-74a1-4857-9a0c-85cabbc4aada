"""
Console utilities for rich output formatting.

This module provides console output utilities using the Rich library
for beautiful terminal output.
"""

import sys
from typing import Optional
from rich.console import Console
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.panel import Panel
from rich.text import Text

from message import Message

# Silent console class that suppresses all output
class SilentConsole:
    """A console that suppresses all output for silent operation."""

    def print(self, *args, **kwargs):
        """Suppress all print calls."""
        pass

    def clear(self):
        """Suppress clear calls."""
        pass

    @property
    def size(self):
        """Return default size."""
        class Size:
            width = 80
            height = 24
        return Size()

# Global console instance - silent for no output
console = SilentConsole()


def print_message(message: Message, end: str = "\n") -> None:
    """
    Print a message with appropriate formatting - DISABLED for silent operation.

    Args:
        message: Message to print
        end: String to append at the end
    """
    # All output is suppressed for silent operation
    pass


def print_user_message(message: Message, end: str = "\n") -> None:
    """Print a user message - DISABLED for silent operation."""
    pass


def print_assistant_message(message: Message, end: str = "\n") -> None:
    """Print an assistant message - DISABLED for silent operation."""
    pass


def print_system_message(message: Message, end: str = "\n") -> None:
    """Print a system message - DISABLED for silent operation."""
    pass


def print_formatted_content(content: str, end: str = "\n") -> None:
    """Print content with code block formatting - DISABLED for silent operation."""
    pass


def print_error(message: str) -> None:
    """Print an error message - DISABLED for silent operation."""
    pass


def print_warning(message: str) -> None:
    """Print a warning message - DISABLED for silent operation."""
    pass


def print_info(message: str) -> None:
    """Print an info message - DISABLED for silent operation."""
    pass


def print_success(message: str) -> None:
    """Print a success message - DISABLED for silent operation."""
    pass


def print_panel(content: str, title: Optional[str] = None, style: str = "blue") -> None:
    """Print content in a panel - DISABLED for silent operation."""
    pass


def print_markdown(content: str) -> None:
    """Print markdown content - DISABLED for silent operation."""
    pass


def print_separator(char: str = "─", length: Optional[int] = None) -> None:
    """Print a separator line - DISABLED for silent operation."""
    pass


def clear_screen() -> None:
    """Clear the console screen - DISABLED for silent operation."""
    pass


def get_terminal_size() -> tuple[int, int]:
    """Get terminal size as (width, height)."""
    return 80, 24  # Return default size since console is disabled


def print_table(data: list[list[str]], headers: Optional[list[str]] = None) -> None:
    """Print data in a table format - DISABLED for silent operation."""
    pass


def print_progress(message: str) -> None:
    """Print a progress message - DISABLED for silent operation."""
    pass


def print_spinner(message: str) -> None:
    """Print a message with spinner - DISABLED for silent operation."""
    pass


__all__ = [
    "console",
    "print_message",
    "print_user_message",
    "print_assistant_message", 
    "print_system_message",
    "print_formatted_content",
    "print_error",
    "print_warning",
    "print_info",
    "print_success",
    "print_panel",
    "print_markdown",
    "print_separator",
    "clear_screen",
    "get_terminal_size",
    "print_table",
    "print_progress",
    "print_spinner",
]
