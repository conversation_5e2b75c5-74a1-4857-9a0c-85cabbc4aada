#!/usr/bin/env python3
"""
Entry point for running the Advanced AI Agent as a module.

This allows running the agent with: python -m aiagent
"""

import sys
import os
import logging
import warnings
from pathlib import Path

# COMPLETE LOG SUPPRESSION - Must be at the very beginning
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
os.environ['GRPC_VERBOSITY'] = 'NONE'
os.environ['GLOG_minloglevel'] = '3'
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['SELENIUM_LOG_LEVEL'] = '3'

# Suppress all warnings and logging
warnings.filterwarnings("ignore")
warnings.simplefilter("ignore")
logging.disable(logging.CRITICAL)

# Redirect stderr to suppress all error messages
import io
sys.stderr = io.StringIO()

# Add the parent directory to the path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from aiagent.cli import main
except ImportError:
    # Fallback for development
    from cli import main

if __name__ == "__main__":
    main()
