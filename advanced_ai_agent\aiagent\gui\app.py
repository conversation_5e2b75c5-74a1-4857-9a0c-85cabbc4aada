"""
Professional GUI Application for Advanced AI Agent.

This module provides a modern, professional web-based interface
similar to high-quality code editors and AI coding interfaces.
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from werkzeug.serving import WSGIRequestHandler

# Suppress Flask logs
import logging
logging.getLogger('werkzeug').setLevel(logging.ERROR)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('engineio').setLevel(logging.ERROR)

from config import get_config
from chat import generate_response
from message import create_user_message, create_system_message
from tools import get_tools_for_llm, list_available_tools
from reasoning import get_intelligence_engine

class SilentRequestHandler(WSGIRequestHandler):
    """Silent request handler to suppress HTTP logs."""
    def log_request(self, code='-', size='-'):
        pass

class ProfessionalGUI:
    """Professional GUI application for the AI agent."""
    
    def __init__(self):
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'ai-agent-secret-key-2024'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", logger=False, engineio_logger=False)
        
        self.config = get_config()
        self.intelligence = get_intelligence_engine()
        self.active_sessions: Dict[str, Dict] = {}
        
        self._setup_routes()
        self._setup_socket_events()
    
    def _setup_routes(self):
        """Setup Flask routes."""
        
        @self.app.route('/')
        def index():
            """Main application page."""
            return render_template('index.html', 
                                 title="Advanced AI Agent",
                                 tools=list_available_tools())
        
        @self.app.route('/api/config')
        def get_config_api():
            """Get application configuration."""
            return jsonify({
                'model': self.config.llm.model,
                'tools_enabled': self.config.tools.enabled,
                'auto_confirm': self.config.tools.auto_confirm,
                'available_tools': list_available_tools()
            })
        
        @self.app.route('/api/tools')
        def get_tools_api():
            """Get available tools information."""
            tools_info = []
            for tool_name in list_available_tools():
                tools_info.append({
                    'name': tool_name,
                    'enabled': tool_name in self.config.tools.enabled,
                    'description': f"Advanced {tool_name} tool for AI agent"
                })
            return jsonify(tools_info)
        
        @self.app.route('/api/performance')
        def get_performance_api():
            """Get AI agent performance metrics."""
            return jsonify(self.intelligence.performance_metrics)
    
    def _setup_socket_events(self):
        """Setup SocketIO event handlers."""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection."""
            session_id = request.sid
            self.active_sessions[session_id] = {
                'connected_at': datetime.now().isoformat(),
                'messages': [],
                'current_task': None
            }
            join_room(session_id)
            
            emit('connected', {
                'session_id': session_id,
                'status': 'Connected to Advanced AI Agent',
                'timestamp': datetime.now().isoformat()
            })
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection."""
            session_id = request.sid
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            leave_room(session_id)
        
        @self.socketio.on('send_message')
        def handle_message(data):
            """Handle incoming message from client."""
            session_id = request.sid
            message_text = data.get('message', '').strip()
            
            if not message_text:
                return
            
            # Store user message
            user_message = {
                'role': 'user',
                'content': message_text,
                'timestamp': datetime.now().isoformat(),
                'id': f"msg_{len(self.active_sessions[session_id]['messages'])}"
            }
            
            self.active_sessions[session_id]['messages'].append(user_message)
            
            # Emit user message to client
            emit('message_received', user_message, room=session_id)
            
            # Generate AI response
            self._generate_ai_response(session_id, message_text)
        
        @self.socketio.on('clear_conversation')
        def handle_clear():
            """Handle conversation clear request."""
            session_id = request.sid
            if session_id in self.active_sessions:
                self.active_sessions[session_id]['messages'] = []
                emit('conversation_cleared', {'status': 'success'}, room=session_id)
        
        @self.socketio.on('get_suggestions')
        def handle_suggestions(data):
            """Handle request for input suggestions."""
            session_id = request.sid
            context = data.get('context', '')
            
            suggestions = self._generate_suggestions(context)
            emit('suggestions', {'suggestions': suggestions}, room=session_id)
    
    def _generate_ai_response(self, session_id: str, user_message: str):
        """Generate AI response for user message."""
        try:
            # Emit typing indicator
            self.socketio.emit('typing_start', room=session_id)
            
            # Create message objects
            messages = []
            for msg in self.active_sessions[session_id]['messages']:
                if msg['role'] == 'user':
                    messages.append(create_user_message(msg['content']))
                elif msg['role'] == 'assistant':
                    messages.append(create_system_message(msg['content']))
            
            # Generate response using the chat system
            response_content = ""
            tools = get_tools_for_llm()
            
            # Use the enhanced generate_response function
            for response_chunk in generate_response(
                messages=messages,
                model=self.config.llm.model,
                stream=False,  # Disable streaming for instant response
                tools=tools
            ):
                if hasattr(response_chunk, 'content'):
                    response_content += response_chunk.content
            
            # Create assistant message
            assistant_message = {
                'role': 'assistant',
                'content': response_content,
                'timestamp': datetime.now().isoformat(),
                'id': f"msg_{len(self.active_sessions[session_id]['messages'])}"
            }
            
            # Store assistant message
            self.active_sessions[session_id]['messages'].append(assistant_message)
            
            # Emit response to client (instant, no streaming)
            self.socketio.emit('typing_stop', room=session_id)
            self.socketio.emit('message_response', assistant_message, room=session_id)
            
        except Exception as e:
            # Emit error message
            self.socketio.emit('typing_stop', room=session_id)
            self.socketio.emit('error', {
                'message': f'Error generating response: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }, room=session_id)
    
    def _generate_suggestions(self, context: str) -> List[str]:
        """Generate input suggestions based on context."""
        suggestions = [
            "Create a Python script that...",
            "Help me debug this code:",
            "Explain how to...",
            "Write a function to...",
            "Analyze this file:",
            "Search for information about...",
            "Generate documentation for...",
            "Optimize this code:",
            "Create a web scraper for...",
            "Set up a development environment for..."
        ]
        
        # Filter suggestions based on context
        if context:
            context_lower = context.lower()
            filtered = []
            for suggestion in suggestions:
                if any(word in suggestion.lower() for word in context_lower.split()):
                    filtered.append(suggestion)
            return filtered[:5] if filtered else suggestions[:5]
        
        return suggestions[:5]
    
    def run(self, host: str = '127.0.0.1', port: int = 5000, debug: bool = False):
        """Run the GUI application."""
        print(f"Starting Advanced AI Agent GUI on http://{host}:{port}")
        
        # Suppress all Flask/SocketIO logs
        if not debug:
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.ERROR)
            
            # Use silent request handler
            self.socketio.run(
                self.app, 
                host=host, 
                port=port, 
                debug=False,
                request_handler=SilentRequestHandler
            )
        else:
            self.socketio.run(self.app, host=host, port=port, debug=debug)

# Global GUI instance
_gui_app: Optional[ProfessionalGUI] = None

def get_gui_app() -> ProfessionalGUI:
    """Get the global GUI application instance."""
    global _gui_app
    if _gui_app is None:
        _gui_app = ProfessionalGUI()
    return _gui_app

def start_gui(host: str = '127.0.0.1', port: int = 5000, debug: bool = False):
    """Start the professional GUI application."""
    app = get_gui_app()
    app.run(host=host, port=port, debug=debug)
