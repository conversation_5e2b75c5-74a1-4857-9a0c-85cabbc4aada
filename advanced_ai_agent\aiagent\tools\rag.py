"""
RAG (Retrieval-Augmented Generation) tool.

This tool provides document retrieval, context-aware search capabilities,
and intelligent document indexing for enhanced AI responses.
"""

import logging
import os
import json
import hashlib
from typing import Generator, Dict, Any, List
from pathlib import Path
from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)

class RAGTool(ToolSpec):
    """Advanced RAG tool for document retrieval and context search."""

    def __init__(self):
        super().__init__(
            name="rag",
            description="Search and retrieve relevant documents, index content, and provide context-aware responses",
            parameters=[
                Parameter("action", "str", "Action: search, index, retrieve, summarize", required=True),
                Parameter("query", "str", "Search query or text to process", required=False),
                Parameter("path", "str", "File or directory path to index", required=False),
                Parameter("collection", "str", "Collection name for organized storage", required=False),
                Parameter("limit", "int", "Maximum number of results", required=False, default=5),
                Parameter("threshold", "float", "Similarity threshold (0.0-1.0)", required=False, default=0.7)
            ],
            block_types=["rag", "search", "retrieve", "index", "context"]
        )
        self.index_dir = Path("rag_index")
        self.index_dir.mkdir(exist_ok=True)
        self.documents = {}
        self.embeddings = {}
        self._load_index()

    def is_available(self) -> bool:
        """Check if RAG tool is available."""
        return True  # Basic functionality always available

    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute RAG operations."""
        try:
            action_info = self._parse_rag_command(content)
            action = action_info.get("action", "search")

            if action == "search":
                yield from self._search_documents(action_info)
            elif action == "index":
                yield from self._index_content(action_info)
            elif action == "retrieve":
                yield from self._retrieve_context(action_info)
            elif action == "summarize":
                yield from self._summarize_documents(action_info)
            elif action == "list":
                yield from self._list_collections(action_info)
            elif action == "clear":
                yield from self._clear_index(action_info)
            else:
                yield self.create_response(f"Unknown RAG action: {action}")

        except Exception as e:
            yield self.create_response(f"RAG operation error: {str(e)}")

    def _parse_rag_command(self, content: str) -> Dict[str, Any]:
        """Parse RAG command from content."""
        lines = content.strip().split('\n')
        action_info = {"action": "search"}

        for line in lines:
            line = line.strip()
            if line.startswith('action:'):
                action_info["action"] = line.split(':', 1)[1].strip()
            elif line.startswith('query:'):
                action_info["query"] = line.split(':', 1)[1].strip()
            elif line.startswith('path:'):
                action_info["path"] = line.split(':', 1)[1].strip()
            elif line.startswith('collection:'):
                action_info["collection"] = line.split(':', 1)[1].strip()
            elif line.startswith('limit:'):
                action_info["limit"] = int(line.split(':', 1)[1].strip())
            elif line.startswith('threshold:'):
                action_info["threshold"] = float(line.split(':', 1)[1].strip())
            elif not line.startswith(('action:', 'query:', 'path:', 'collection:', 'limit:', 'threshold:')) and line:
                # Treat as search query
                action_info["query"] = line

        return action_info

    def _search_documents(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Search indexed documents."""
        query = action_info.get("query", "")
        if not query:
            yield self.create_response("Error: No search query provided")
            return

        collection = action_info.get("collection", "default")
        limit = action_info.get("limit", 5)
        threshold = action_info.get("threshold", 0.7)

        try:
            # Simple keyword-based search (can be enhanced with embeddings)
            results = []
            query_lower = query.lower()

            for doc_id, doc_data in self.documents.items():
                if collection != "default" and doc_data.get("collection") != collection:
                    continue

                content = doc_data.get("content", "").lower()
                title = doc_data.get("title", "").lower()

                # Calculate simple relevance score
                score = 0
                query_words = query_lower.split()

                for word in query_words:
                    if word in title:
                        score += 2  # Title matches are more important
                    if word in content:
                        score += 1

                if score > 0:
                    results.append({
                        "doc_id": doc_id,
                        "title": doc_data.get("title", "Untitled"),
                        "content": doc_data.get("content", "")[:500] + "...",
                        "path": doc_data.get("path", ""),
                        "score": score,
                        "collection": doc_data.get("collection", "default")
                    })

            # Sort by score and limit results
            results.sort(key=lambda x: x["score"], reverse=True)
            results = results[:limit]

            if results:
                response = f"Found {len(results)} relevant documents for query: '{query}'\n\n"
                for i, result in enumerate(results, 1):
                    response += f"{i}. **{result['title']}** (Score: {result['score']})\n"
                    response += f"   Collection: {result['collection']}\n"
                    response += f"   Path: {result['path']}\n"
                    response += f"   Content: {result['content']}\n\n"

                yield self.create_response(response)
            else:
                yield self.create_response(f"No relevant documents found for query: '{query}'")

        except Exception as e:
            yield self.create_response(f"Search error: {str(e)}")

    def _index_content(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Index content from files or directories."""
        path = action_info.get("path", "")
        if not path:
            yield self.create_response("Error: No path provided for indexing")
            return

        collection = action_info.get("collection", "default")

        try:
            path_obj = Path(path)
            if not path_obj.exists():
                yield self.create_response(f"Error: Path does not exist: {path}")
                return

            indexed_count = 0

            if path_obj.is_file():
                # Index single file
                if self._index_file(path_obj, collection):
                    indexed_count = 1
            else:
                # Index directory
                for file_path in path_obj.rglob("*"):
                    if file_path.is_file() and self._is_text_file(file_path):
                        if self._index_file(file_path, collection):
                            indexed_count += 1

            self._save_index()

            yield self.create_response(
                f"Successfully indexed {indexed_count} files from '{path}' into collection '{collection}'"
            )

        except Exception as e:
            yield self.create_response(f"Indexing error: {str(e)}")

    def _index_file(self, file_path: Path, collection: str) -> bool:
        """Index a single file."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Create document ID
            doc_id = hashlib.md5(str(file_path).encode()).hexdigest()

            # Store document
            self.documents[doc_id] = {
                "title": file_path.name,
                "content": content,
                "path": str(file_path),
                "collection": collection,
                "size": len(content),
                "indexed_at": str(Path().cwd())
            }

            return True

        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {e}")
            return False

    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file that can be indexed."""
        text_extensions = {
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml',
            '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.log',
            '.rst', '.tex', '.csv', '.sql', '.sh', '.bat', '.ps1'
        }
        return file_path.suffix.lower() in text_extensions

    def _retrieve_context(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Retrieve context for a specific query."""
        query = action_info.get("query", "")
        if not query:
            yield self.create_response("Error: No query provided for context retrieval")
            return

        # Use search to find relevant context
        search_results = []
        query_lower = query.lower()

        for doc_id, doc_data in self.documents.items():
            content = doc_data.get("content", "").lower()
            if any(word in content for word in query_lower.split()):
                search_results.append(doc_data)

        if search_results:
            context = "Relevant context:\n\n"
            for doc in search_results[:3]:  # Limit to top 3 results
                context += f"From {doc['title']}:\n"
                context += f"{doc['content'][:800]}...\n\n"

            yield self.create_response(context)
        else:
            yield self.create_response("No relevant context found")

    def _summarize_documents(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Summarize documents in a collection."""
        collection = action_info.get("collection", "default")

        try:
            docs_in_collection = [
                doc for doc in self.documents.values()
                if doc.get("collection") == collection
            ]

            if not docs_in_collection:
                yield self.create_response(f"No documents found in collection: {collection}")
                return

            summary = f"Summary of collection '{collection}':\n\n"
            summary += f"Total documents: {len(docs_in_collection)}\n"

            total_size = sum(doc.get("size", 0) for doc in docs_in_collection)
            summary += f"Total content size: {total_size} characters\n\n"

            summary += "Documents:\n"
            for doc in docs_in_collection:
                summary += f"- {doc['title']} ({doc.get('size', 0)} chars)\n"
                summary += f"  Path: {doc['path']}\n"

            yield self.create_response(summary)

        except Exception as e:
            yield self.create_response(f"Summarization error: {str(e)}")

    def _list_collections(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """List all collections and their contents."""
        try:
            collections = {}
            for doc_data in self.documents.values():
                collection = doc_data.get("collection", "default")
                if collection not in collections:
                    collections[collection] = []
                collections[collection].append(doc_data)

            if not collections:
                yield self.create_response("No collections found")
                return

            response = "Available collections:\n\n"
            for collection, docs in collections.items():
                response += f"**{collection}** ({len(docs)} documents)\n"
                for doc in docs[:5]:  # Show first 5 documents
                    response += f"  - {doc['title']}\n"
                if len(docs) > 5:
                    response += f"  ... and {len(docs) - 5} more\n"
                response += "\n"

            yield self.create_response(response)

        except Exception as e:
            yield self.create_response(f"List collections error: {str(e)}")

    def _clear_index(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Clear the document index."""
        collection = action_info.get("collection")

        try:
            if collection:
                # Clear specific collection
                docs_to_remove = [
                    doc_id for doc_id, doc_data in self.documents.items()
                    if doc_data.get("collection") == collection
                ]

                for doc_id in docs_to_remove:
                    del self.documents[doc_id]

                yield self.create_response(f"Cleared {len(docs_to_remove)} documents from collection '{collection}'")
            else:
                # Clear all documents
                count = len(self.documents)
                self.documents.clear()
                self.embeddings.clear()

                yield self.create_response(f"Cleared all {count} documents from index")

            self._save_index()

        except Exception as e:
            yield self.create_response(f"Clear index error: {str(e)}")

    def _load_index(self):
        """Load the document index from disk."""
        try:
            index_file = self.index_dir / "documents.json"
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    self.documents = json.load(f)
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            self.documents = {}

    def _save_index(self):
        """Save the document index to disk."""
        try:
            index_file = self.index_dir / "documents.json"
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving index: {e}")

# Create tool instance
rag_tool = RAGTool()
