"""
Computer use tool for desktop automation.

This tool provides computer use capabilities for GUI automation,
screen capture, and desktop interaction.
"""

import logging
import os
import sys
import subprocess
from typing import Generator, Dict, Any
from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)

class ComputerTool(ToolSpec):
    """Advanced computer automation tool."""

    def __init__(self):
        super().__init__(
            name="computer",
            description="Interact with desktop applications, take screenshots, and automate GUI tasks",
            parameters=[
                Parameter("action", "str", "Action: screenshot, click, type, key, app, window", required=True),
                Parameter("x", "int", "X coordinate for click actions", required=False),
                Parameter("y", "int", "Y coordinate for click actions", required=False),
                Parameter("text", "str", "Text to type", required=False),
                Parameter("key", "str", "Key to press (enter, space, ctrl+c, etc.)", required=False),
                Parameter("app", "str", "Application to launch", required=False),
                Parameter("window", "str", "Window title to interact with", required=False)
            ],
            block_types=["computer", "desktop", "gui", "automation"]
        )

    def is_available(self) -> bool:
        """Check if computer tool is available."""
        return True  # Basic functionality always available

    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute computer automation tasks."""
        try:
            action_info = self._parse_computer_command(content)
            action = action_info.get("action", "screenshot")

            if action == "screenshot":
                yield from self._take_screenshot(action_info)
            elif action == "click":
                yield from self._click(action_info)
            elif action == "type":
                yield from self._type_text(action_info)
            elif action == "key":
                yield from self._press_key(action_info)
            elif action == "app":
                yield from self._launch_app(action_info)
            elif action == "window":
                yield from self._window_action(action_info)
            elif action == "system":
                yield from self._system_info(action_info)
            else:
                yield self.create_response(f"Unknown computer action: {action}")

        except Exception as e:
            yield self.create_response(f"Computer automation error: {str(e)}")

    def _parse_computer_command(self, content: str) -> Dict[str, Any]:
        """Parse computer command from content."""
        lines = content.strip().split('\n')
        action_info = {"action": "screenshot"}

        for line in lines:
            line = line.strip()
            if line.startswith('action:'):
                action_info["action"] = line.split(':', 1)[1].strip()
            elif line.startswith('x:'):
                action_info["x"] = int(line.split(':', 1)[1].strip())
            elif line.startswith('y:'):
                action_info["y"] = int(line.split(':', 1)[1].strip())
            elif line.startswith('text:'):
                action_info["text"] = line.split(':', 1)[1].strip()
            elif line.startswith('key:'):
                action_info["key"] = line.split(':', 1)[1].strip()
            elif line.startswith('app:'):
                action_info["app"] = line.split(':', 1)[1].strip()
            elif line.startswith('window:'):
                action_info["window"] = line.split(':', 1)[1].strip()
            elif not line.startswith(('action:', 'x:', 'y:', 'text:', 'key:', 'app:', 'window:')) and line:
                # Treat as text to type or app to launch
                if action_info.get("action") == "type":
                    action_info["text"] = line
                elif action_info.get("action") == "app":
                    action_info["app"] = line

        return action_info

    def _take_screenshot(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Take a screenshot of the desktop."""
        try:
            if sys.platform == "win32":
                # Windows screenshot using built-in tools
                import tempfile
                screenshot_path = os.path.join(tempfile.gettempdir(), "screenshot.png")

                # Use PowerShell to take screenshot
                ps_command = f'''
                Add-Type -AssemblyName System.Windows.Forms
                Add-Type -AssemblyName System.Drawing
                $Screen = [System.Windows.Forms.SystemInformation]::VirtualScreen
                $Width = $Screen.Width
                $Height = $Screen.Height
                $Left = $Screen.Left
                $Top = $Screen.Top
                $bitmap = New-Object System.Drawing.Bitmap $Width, $Height
                $graphic = [System.Drawing.Graphics]::FromImage($bitmap)
                $graphic.CopyFromScreen($Left, $Top, 0, 0, $bitmap.Size)
                $bitmap.Save("{screenshot_path}")
                '''

                result = subprocess.run(
                    ["powershell", "-Command", ps_command],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0 and os.path.exists(screenshot_path):
                    yield self.create_response(f"Screenshot saved to: {screenshot_path}")
                else:
                    yield self.create_response("Failed to take screenshot using PowerShell")

            elif sys.platform.startswith("linux"):
                # Linux screenshot using scrot or gnome-screenshot
                try:
                    result = subprocess.run(
                        ["scrot", "/tmp/screenshot.png"],
                        capture_output=True,
                        timeout=10
                    )
                    if result.returncode == 0:
                        yield self.create_response("Screenshot saved to: /tmp/screenshot.png")
                    else:
                        # Try gnome-screenshot
                        result = subprocess.run(
                            ["gnome-screenshot", "-f", "/tmp/screenshot.png"],
                            capture_output=True,
                            timeout=10
                        )
                        if result.returncode == 0:
                            yield self.create_response("Screenshot saved to: /tmp/screenshot.png")
                        else:
                            yield self.create_response("Screenshot tools not available (install scrot or gnome-screenshot)")
                except FileNotFoundError:
                    yield self.create_response("Screenshot tools not found. Install scrot or gnome-screenshot")

            elif sys.platform == "darwin":
                # macOS screenshot using screencapture
                result = subprocess.run(
                    ["screencapture", "/tmp/screenshot.png"],
                    capture_output=True,
                    timeout=10
                )
                if result.returncode == 0:
                    yield self.create_response("Screenshot saved to: /tmp/screenshot.png")
                else:
                    yield self.create_response("Failed to take screenshot on macOS")

            else:
                yield self.create_response(f"Screenshot not supported on platform: {sys.platform}")

        except Exception as e:
            yield self.create_response(f"Screenshot error: {str(e)}")

    def _click(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Simulate mouse click."""
        x = action_info.get("x")
        y = action_info.get("y")

        if x is None or y is None:
            yield self.create_response("Error: x and y coordinates required for click action")
            return

        try:
            if sys.platform == "win32":
                # Windows click simulation
                import ctypes
                ctypes.windll.user32.SetCursorPos(x, y)
                ctypes.windll.user32.mouse_event(2, 0, 0, 0, 0)  # Left button down
                ctypes.windll.user32.mouse_event(4, 0, 0, 0, 0)  # Left button up
                yield self.create_response(f"Clicked at coordinates ({x}, {y})")

            else:
                yield self.create_response("Mouse click simulation requires additional libraries on this platform")

        except Exception as e:
            yield self.create_response(f"Click error: {str(e)}")

    def _type_text(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Type text."""
        text = action_info.get("text", "")
        if not text:
            yield self.create_response("Error: No text provided to type")
            return

        try:
            if sys.platform == "win32":
                # Windows text input simulation
                import time
                for char in text:
                    if char == '\n':
                        # Press Enter
                        import ctypes
                        ctypes.windll.user32.keybd_event(0x0D, 0, 0, 0)  # Key down
                        ctypes.windll.user32.keybd_event(0x0D, 0, 2, 0)  # Key up
                    else:
                        # Type character (simplified - would need full implementation)
                        pass
                    time.sleep(0.01)

                yield self.create_response(f"Typed text: {text[:50]}...")

            else:
                yield self.create_response("Text typing simulation requires additional libraries on this platform")

        except Exception as e:
            yield self.create_response(f"Type error: {str(e)}")

    def _press_key(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Press a key or key combination."""
        key = action_info.get("key", "")
        if not key:
            yield self.create_response("Error: No key specified")
            return

        yield self.create_response(f"Key press simulation not fully implemented for: {key}")

    def _launch_app(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Launch an application."""
        app = action_info.get("app", "")
        if not app:
            yield self.create_response("Error: No application specified")
            return

        try:
            if sys.platform == "win32":
                # Windows app launch
                subprocess.Popen(app, shell=True)
                yield self.create_response(f"Launched application: {app}")

            elif sys.platform.startswith("linux"):
                # Linux app launch
                subprocess.Popen([app])
                yield self.create_response(f"Launched application: {app}")

            elif sys.platform == "darwin":
                # macOS app launch
                subprocess.Popen(["open", "-a", app])
                yield self.create_response(f"Launched application: {app}")

            else:
                yield self.create_response(f"App launch not supported on platform: {sys.platform}")

        except Exception as e:
            yield self.create_response(f"App launch error: {str(e)}")

    def _window_action(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform window management actions."""
        window = action_info.get("window", "")
        yield self.create_response(f"Window management not fully implemented for: {window}")

    def _system_info(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Get system information."""
        try:
            import platform
            import psutil

            info = f"System Information:\n"
            info += f"Platform: {platform.system()} {platform.release()}\n"
            info += f"Architecture: {platform.architecture()[0]}\n"
            info += f"Processor: {platform.processor()}\n"
            info += f"CPU Cores: {psutil.cpu_count()}\n"
            info += f"Memory: {psutil.virtual_memory().total // (1024**3)} GB\n"
            info += f"Disk Usage: {psutil.disk_usage('/').percent}%\n"

            yield self.create_response(info)

        except ImportError:
            # Basic info without psutil
            import platform
            info = f"System Information:\n"
            info += f"Platform: {platform.system()} {platform.release()}\n"
            info += f"Architecture: {platform.architecture()[0]}\n"
            info += f"Python: {platform.python_version()}\n"

            yield self.create_response(info)
        except Exception as e:
            yield self.create_response(f"System info error: {str(e)}")

# Create tool instance
computer_tool = ComputerTool()
