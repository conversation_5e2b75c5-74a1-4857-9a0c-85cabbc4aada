#!/usr/bin/env python3
"""
Comprehensive test suite for the enhanced AI agent system.

This script tests all the improvements made to the AI agent:
1. Logging system (disabled)
2. Infinite loop fixes
3. Enhanced intelligence and tool orchestration
4. Complete tool implementations
5. Optimized tool execution and response handling
"""

import sys
import os
import tempfile
import subprocess
from pathlib import Path

# Add the aiagent directory to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_logging_disabled():
    """Test that logging is properly disabled."""
    print("Testing logging system...")
    
    try:
        from utils.logging_setup import setup_logging
        from utils.console import console
        
        # Setup logging - should be silent
        setup_logging(verbose=True, disable_console_logs=False)
        
        # Test console output - should be suppressed
        console.print("This should not appear")
        
        print("✓ Logging system properly disabled")
        return True
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_tool_availability():
    """Test that all tools are available and properly implemented."""
    print("Testing tool availability...")
    
    try:
        from tools import init_tools, list_available_tools
        
        # Initialize tools
        tools = init_tools()
        available_tools = list_available_tools()
        
        expected_tools = [
            "shell", "code", "file", "web", "browser",
            "computer", "github", "rag", "codebase"
        ]
        
        missing_tools = []
        for tool_name in expected_tools:
            if tool_name not in available_tools:
                missing_tools.append(tool_name)
        
        if missing_tools:
            print(f"✗ Missing tools: {missing_tools}")
            return False
        
        print(f"✓ All {len(available_tools)} tools available: {available_tools}")
        return True
    except Exception as e:
        print(f"✗ Tool availability test failed: {e}")
        return False

def test_tool_execution():
    """Test basic tool execution functionality."""
    print("Testing tool execution...")
    
    try:
        from tools import get_tool
        from message import create_assistant_message
        
        # Test shell tool
        shell_tool = get_tool("shell")
        if shell_tool and shell_tool.is_available():
            test_msg = create_assistant_message("```shell\necho 'Hello World'\n```")
            responses = list(shell_tool.execute("echo 'Hello World'"))
            if responses:
                print("✓ Shell tool execution working")
            else:
                print("✗ Shell tool execution failed")
                return False
        
        # Test code tool (python)
        code_tool = get_tool("code")
        if code_tool and code_tool.is_available():
            responses = list(code_tool.execute("print('Python test')"))
            if responses:
                print("✓ Code tool execution working")
            else:
                print("✗ Code tool execution failed")
                return False
        
        print("✓ Tool execution tests passed")
        return True
    except Exception as e:
        print(f"✗ Tool execution test failed: {e}")
        return False

def test_configuration():
    """Test enhanced configuration settings."""
    print("Testing configuration...")
    
    try:
        from config import get_config
        
        config = get_config()
        
        # Check that auto_confirm is enabled for autonomous operation
        if not config.tools.auto_confirm:
            print("✗ Auto-confirm not enabled")
            return False
        
        # Check increased timeouts
        if config.tools.timeout < 600:
            print("✗ Tool timeout not increased")
            return False
        
        # Check increased output length
        if config.tools.max_output_length < 50000:
            print("✗ Max output length not increased")
            return False
        
        print("✓ Configuration properly enhanced")
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_message_processing():
    """Test enhanced message processing."""
    print("Testing message processing...")
    
    try:
        from message import (
            create_user_message, create_assistant_message, create_system_message,
            create_tool_response_message, create_error_message, create_progress_message,
            merge_streaming_messages
        )
        
        # Test basic message creation
        user_msg = create_user_message("Test message")
        assistant_msg = create_assistant_message("Response")
        system_msg = create_system_message("System message")
        
        # Test enhanced message types
        tool_msg = create_tool_response_message("Tool output", "shell", True)
        error_msg = create_error_message("Test error", "Test context")
        progress_msg = create_progress_message("Processing", 1, 3)
        
        # Test message merging
        messages = [
            create_assistant_message("Part 1"),
            create_assistant_message("Part 2"),
            create_assistant_message("Part 3")
        ]
        merged = merge_streaming_messages(messages)
        
        if merged.content != "Part 1Part 2Part 3":
            print("✗ Message merging failed")
            return False
        
        print("✓ Message processing enhancements working")
        return True
    except Exception as e:
        print(f"✗ Message processing test failed: {e}")
        return False

def test_system_prompt():
    """Test enhanced system prompt."""
    print("Testing system prompt...")
    
    try:
        from utils.prompt import create_system_prompt
        
        tools = ["shell", "python", "file", "web"]
        prompt = create_system_prompt(tools)
        
        # Check for enhanced keywords
        required_keywords = [
            "AUTONOMOUS", "INTELLIGENT", "PROACTIVITY", 
            "COMPLETION", "TOOL MASTERY", "PERSISTENCE"
        ]
        
        for keyword in required_keywords:
            if keyword not in prompt:
                print(f"✗ Missing keyword in system prompt: {keyword}")
                return False
        
        print("✓ System prompt properly enhanced")
        return True
    except Exception as e:
        print(f"✗ System prompt test failed: {e}")
        return False

def test_chat_loop_improvements():
    """Test that chat loop improvements are in place."""
    print("Testing chat loop improvements...")
    
    try:
        # Check that the chat.py file has the improvements
        chat_file = Path(__file__).parent / "chat.py"
        if not chat_file.exists():
            print("✗ Chat file not found")
            return False
        
        with open(chat_file, 'r') as f:
            content = f.read()
        
        # Check for key improvements
        improvements = [
            "max_iterations",
            "task_completion_indicators", 
            "accumulated_content",
            "tool_executed",
            "enhanced_messages"
        ]
        
        for improvement in improvements:
            if improvement not in content:
                print(f"✗ Missing chat loop improvement: {improvement}")
                return False
        
        print("✓ Chat loop improvements in place")
        return True
    except Exception as e:
        print(f"✗ Chat loop test failed: {e}")
        return False

def run_integration_test():
    """Run a simple integration test."""
    print("Running integration test...")
    
    try:
        from chat import chat
        from message import create_user_message, create_system_message
        from config import get_config
        
        # Create a simple test scenario
        config = get_config()
        
        # Test messages
        initial_messages = [
            create_system_message("Test system message"),
            create_user_message("echo 'Integration test'")
        ]
        
        # This would normally run the chat loop, but we'll just verify setup
        print("✓ Integration test setup successful")
        return True
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("ENHANCED AI AGENT SYSTEM - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_logging_disabled,
        test_tool_availability,
        test_tool_execution,
        test_configuration,
        test_message_processing,
        test_system_prompt,
        test_chat_loop_improvements,
        run_integration_test
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Enhanced AI Agent system is ready!")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
