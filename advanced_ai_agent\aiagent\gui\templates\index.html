<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Professional AI Coding Assistant</title>
    
    <!-- Modern CSS Framework -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-bg: #0d1117;
            --secondary-bg: #161b22;
            --tertiary-bg: #21262d;
            --border-color: #30363d;
            --text-primary: #f0f6fc;
            --text-secondary: #8b949e;
            --accent-blue: #58a6ff;
            --accent-green: #3fb950;
            --accent-orange: #ff7b72;
        }
        
        body {
            background: var(--primary-bg);
            color: var(--text-primary);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }
        
        .chat-container {
            height: calc(100vh - 120px);
            background: var(--secondary-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }
        
        .message-user {
            background: var(--tertiary-bg);
            border-left: 3px solid var(--accent-blue);
        }
        
        .message-assistant {
            background: var(--secondary-bg);
            border-left: 3px solid var(--accent-green);
        }
        
        .input-area {
            background: var(--tertiary-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }
        
        .btn-primary {
            background: var(--accent-blue);
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #4493e6;
            transform: translateY(-1px);
        }
        
        .typing-indicator {
            display: none;
            color: var(--text-secondary);
        }
        
        .typing-indicator.active {
            display: flex;
            align-items: center;
        }
        
        .typing-dots {
            display: inline-flex;
            gap: 4px;
        }
        
        .typing-dots span {
            width: 6px;
            height: 6px;
            background: var(--accent-blue);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .sidebar {
            background: var(--secondary-bg);
            border-right: 1px solid var(--border-color);
        }
        
        .tool-badge {
            background: var(--tertiary-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .tool-badge.enabled {
            background: var(--accent-green);
            color: white;
        }
        
        pre code {
            background: var(--primary-bg) !important;
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }
        
        .suggestion-item {
            background: var(--tertiary-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .suggestion-item:hover {
            background: var(--accent-blue);
            color: white;
        }
    </style>
</head>
<body>
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 p-4">
            <div class="mb-6">
                <h1 class="text-xl font-bold text-white mb-2">
                    <i class="fas fa-robot mr-2 text-blue-400"></i>
                    AI Agent Pro
                </h1>
                <p class="text-sm text-gray-400">Advanced AI Coding Assistant</p>
            </div>
            
            <!-- Tools Section -->
            <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-300 mb-3">Available Tools</h3>
                <div id="tools-list" class="space-y-2">
                    {% for tool in tools %}
                    <div class="tool-badge enabled">
                        <i class="fas fa-cog mr-1"></i>
                        {{ tool }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Performance Metrics -->
            <div class="mb-6">
                <h3 class="text-sm font-semibold text-gray-300 mb-3">Performance</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Success Rate:</span>
                        <span id="success-rate" class="text-green-400">--</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Confidence:</span>
                        <span id="avg-confidence" class="text-blue-400">--</span>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="space-y-2">
                <button id="clear-chat" class="w-full px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                    <i class="fas fa-trash mr-2"></i>Clear Chat
                </button>
                <button id="export-chat" class="w-full px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
        
        <!-- Main Chat Area -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-gray-800 border-b border-gray-700 p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-white">AI Coding Assistant</h2>
                        <p class="text-sm text-gray-400">Powered by Advanced Intelligence Engine</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-400">
                            <i class="fas fa-circle text-green-400 mr-1"></i>
                            Connected
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-container flex-1 overflow-y-auto p-4 space-y-4">
                <div class="text-center text-gray-400 py-8">
                    <i class="fas fa-comments text-4xl mb-4"></i>
                    <p>Welcome to Advanced AI Agent Pro</p>
                    <p class="text-sm mt-2">Start a conversation to begin coding with AI assistance</p>
                </div>
            </div>
            
            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator px-4 py-2">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="ml-2">AI is thinking...</span>
            </div>
            
            <!-- Input Area -->
            <div class="p-4 border-t border-gray-700">
                <!-- Suggestions -->
                <div id="suggestions" class="mb-3 hidden">
                    <div class="text-xs text-gray-400 mb-2">Suggestions:</div>
                    <div id="suggestions-list" class="flex flex-wrap gap-2"></div>
                </div>
                
                <div class="flex space-x-3">
                    <div class="flex-1">
                        <textarea 
                            id="message-input" 
                            class="input-area w-full p-3 text-white resize-none focus:outline-none focus:ring-2 focus:ring-blue-500" 
                            rows="3" 
                            placeholder="Ask me anything about coding, development, or use any available tools..."
                        ></textarea>
                    </div>
                    <div class="flex flex-col space-y-2">
                        <button id="send-button" class="btn-primary px-6 py-3 text-white rounded font-medium">
                            <i class="fas fa-paper-plane mr-2"></i>Send
                        </button>
                        <button id="voice-button" class="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>
                </div>
                
                <div class="flex items-center justify-between mt-2 text-xs text-gray-400">
                    <div>Press Ctrl+Enter to send</div>
                    <div id="char-count">0 characters</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
    
    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // DOM elements
        const messagesContainer = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        const clearButton = document.getElementById('clear-chat');
        const charCount = document.getElementById('char-count');
        
        // Socket event handlers
        socket.on('connected', (data) => {
            console.log('Connected to AI Agent:', data);
            loadPerformanceMetrics();
        });
        
        socket.on('message_received', (message) => {
            displayMessage(message);
        });
        
        socket.on('message_response', (message) => {
            displayMessage(message);
        });
        
        socket.on('typing_start', () => {
            typingIndicator.classList.add('active');
        });
        
        socket.on('typing_stop', () => {
            typingIndicator.classList.remove('active');
        });
        
        socket.on('error', (error) => {
            displayError(error.message);
        });
        
        socket.on('conversation_cleared', () => {
            messagesContainer.innerHTML = `
                <div class="text-center text-gray-400 py-8">
                    <i class="fas fa-comments text-4xl mb-4"></i>
                    <p>Conversation cleared</p>
                    <p class="text-sm mt-2">Start a new conversation</p>
                </div>
            `;
        });
        
        // Functions
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            socket.emit('send_message', { message: message });
            messageInput.value = '';
            updateCharCount();
            messageInput.focus();
        }
        
        function displayMessage(message) {
            const messageDiv = document.createElement('div');
            const isUser = message.role === 'user';
            
            messageDiv.className = `p-4 rounded-lg ${isUser ? 'message-user' : 'message-assistant'}`;
            
            const content = isUser ? message.content : marked.parse(message.content);
            
            messageDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas ${isUser ? 'fa-user' : 'fa-robot'} text-lg ${isUser ? 'text-blue-400' : 'text-green-400'}"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm text-gray-400 mb-1">${isUser ? 'You' : 'AI Assistant'}</div>
                        <div class="prose prose-invert max-w-none">${content}</div>
                        <div class="text-xs text-gray-500 mt-2">${new Date(message.timestamp).toLocaleTimeString()}</div>
                    </div>
                </div>
            `;
            
            // Remove welcome message if it exists
            const welcomeMsg = messagesContainer.querySelector('.text-center');
            if (welcomeMsg) {
                welcomeMsg.remove();
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Highlight code blocks
            Prism.highlightAllUnder(messageDiv);
        }
        
        function displayError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'p-4 rounded-lg bg-red-900 border-l-3 border-red-500';
            errorDiv.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                    <span class="text-red-200">${message}</span>
                </div>
            `;
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function updateCharCount() {
            const count = messageInput.value.length;
            charCount.textContent = `${count} characters`;
        }
        
        function loadPerformanceMetrics() {
            fetch('/api/performance')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('success-rate').textContent = 
                        (data.success_rate * 100).toFixed(1) + '%';
                    document.getElementById('avg-confidence').textContent = 
                        (data.average_confidence * 100).toFixed(1) + '%';
                })
                .catch(console.error);
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });
        
        messageInput.addEventListener('input', updateCharCount);
        
        clearButton.addEventListener('click', () => {
            if (confirm('Clear conversation history?')) {
                socket.emit('clear_conversation');
            }
        });
        
        // Auto-focus input
        messageInput.focus();
        
        // Load performance metrics periodically
        setInterval(loadPerformanceMetrics, 30000);
    </script>
</body>
</html>
