"""
Advanced Token Management System for AI Agent.

This module provides intelligent token usage optimization, tracking,
and management to prevent token limit issues and reduce costs.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

class TokenLimitStrategy(Enum):
    """Strategies for handling token limits."""
    TRUNCATE_OLDEST = "truncate_oldest"
    SUMMARIZE_CONTEXT = "summarize_context"
    SMART_COMPRESSION = "smart_compression"
    PRIORITY_BASED = "priority_based"

@dataclass
class TokenUsage:
    """Token usage tracking for a single request."""
    request_id: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    model: str
    timestamp: float
    cost_estimate: float
    context_length: int

@dataclass
class TokenBudget:
    """Token budget configuration."""
    max_tokens_per_request: int = 8192
    max_tokens_per_session: int = 100000
    max_tokens_per_hour: int = 50000
    warning_threshold: float = 0.8  # Warn at 80% usage
    emergency_threshold: float = 0.95  # Emergency at 95% usage

class TokenManager:
    """Advanced token management and optimization system."""
    
    def __init__(self, budget: Optional[TokenBudget] = None):
        self.budget = budget or TokenBudget()
        self.usage_history: List[TokenUsage] = []
        self.session_tokens = 0
        self.hourly_tokens = 0
        self.last_hour_reset = time.time()
        self.cache: Dict[str, Any] = {}
        self.compression_cache: Dict[str, str] = {}
        
        # Token costs per model (approximate)
        self.token_costs = {
            "gemini-2.0-flash-exp": {"input": 0.000001, "output": 0.000002},
            "gemini-1.5-pro": {"input": 0.000003, "output": 0.000006},
            "gemini-1.5-flash": {"input": 0.0000005, "output": 0.000001}
        }
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        # Simple estimation: ~4 characters per token for English
        # More sophisticated tokenization would use actual tokenizer
        return max(1, len(text) // 4)
    
    def check_token_limits(self, estimated_tokens: int, model: str) -> Tuple[bool, str]:
        """
        Check if request would exceed token limits.
        
        Args:
            estimated_tokens: Estimated tokens for the request
            model: Model being used
            
        Returns:
            Tuple of (can_proceed, reason)
        """
        # Reset hourly counter if needed
        current_time = time.time()
        if current_time - self.last_hour_reset > 3600:
            self.hourly_tokens = 0
            self.last_hour_reset = current_time
        
        # Check per-request limit
        if estimated_tokens > self.budget.max_tokens_per_request:
            return False, f"Request exceeds per-request limit ({estimated_tokens} > {self.budget.max_tokens_per_request})"
        
        # Check session limit
        if self.session_tokens + estimated_tokens > self.budget.max_tokens_per_session:
            return False, f"Would exceed session limit ({self.session_tokens + estimated_tokens} > {self.budget.max_tokens_per_session})"
        
        # Check hourly limit
        if self.hourly_tokens + estimated_tokens > self.budget.max_tokens_per_hour:
            return False, f"Would exceed hourly limit ({self.hourly_tokens + estimated_tokens} > {self.budget.max_tokens_per_hour})"
        
        # Check warning thresholds
        session_usage = (self.session_tokens + estimated_tokens) / self.budget.max_tokens_per_session
        hourly_usage = (self.hourly_tokens + estimated_tokens) / self.budget.max_tokens_per_hour
        
        if session_usage > self.budget.emergency_threshold or hourly_usage > self.budget.emergency_threshold:
            return False, "Emergency threshold reached - request blocked"
        
        if session_usage > self.budget.warning_threshold or hourly_usage > self.budget.warning_threshold:
            return True, "Warning: Approaching token limits"
        
        return True, "OK"
    
    def optimize_context(self, messages: List[Any], target_tokens: int, 
                        strategy: TokenLimitStrategy = TokenLimitStrategy.SMART_COMPRESSION) -> List[Any]:
        """
        Optimize context to fit within token limits.
        
        Args:
            messages: List of messages to optimize
            target_tokens: Target token count
            strategy: Optimization strategy
            
        Returns:
            Optimized messages list
        """
        if not messages:
            return messages
        
        current_tokens = sum(self.estimate_tokens(str(msg)) for msg in messages)
        
        if current_tokens <= target_tokens:
            return messages
        
        if strategy == TokenLimitStrategy.TRUNCATE_OLDEST:
            return self._truncate_oldest(messages, target_tokens)
        elif strategy == TokenLimitStrategy.SUMMARIZE_CONTEXT:
            return self._summarize_context(messages, target_tokens)
        elif strategy == TokenLimitStrategy.SMART_COMPRESSION:
            return self._smart_compression(messages, target_tokens)
        elif strategy == TokenLimitStrategy.PRIORITY_BASED:
            return self._priority_based_optimization(messages, target_tokens)
        
        return messages
    
    def track_usage(self, request_id: str, input_tokens: int, output_tokens: int, 
                   model: str, context_length: int) -> TokenUsage:
        """
        Track token usage for a request.
        
        Args:
            request_id: Unique request identifier
            input_tokens: Input tokens used
            output_tokens: Output tokens generated
            model: Model used
            context_length: Context length in characters
            
        Returns:
            TokenUsage object
        """
        total_tokens = input_tokens + output_tokens
        
        # Calculate cost estimate
        cost_estimate = 0.0
        if model in self.token_costs:
            costs = self.token_costs[model]
            cost_estimate = (input_tokens * costs["input"]) + (output_tokens * costs["output"])
        
        usage = TokenUsage(
            request_id=request_id,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            model=model,
            timestamp=time.time(),
            cost_estimate=cost_estimate,
            context_length=context_length
        )
        
        # Update counters
        self.session_tokens += total_tokens
        self.hourly_tokens += total_tokens
        self.usage_history.append(usage)
        
        # Keep only recent history (last 1000 requests)
        if len(self.usage_history) > 1000:
            self.usage_history = self.usage_history[-1000:]
        
        return usage
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive usage statistics.
        
        Returns:
            Dictionary with usage statistics
        """
        if not self.usage_history:
            return {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "session_usage": 0.0,
                "hourly_usage": 0.0
            }
        
        total_requests = len(self.usage_history)
        total_tokens = sum(usage.total_tokens for usage in self.usage_history)
        total_cost = sum(usage.cost_estimate for usage in self.usage_history)
        
        # Recent usage (last hour)
        current_time = time.time()
        recent_usage = [
            usage for usage in self.usage_history 
            if current_time - usage.timestamp < 3600
        ]
        recent_tokens = sum(usage.total_tokens for usage in recent_usage)
        
        return {
            "total_requests": total_requests,
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "session_tokens": self.session_tokens,
            "session_usage": self.session_tokens / self.budget.max_tokens_per_session,
            "hourly_tokens": recent_tokens,
            "hourly_usage": recent_tokens / self.budget.max_tokens_per_hour,
            "average_tokens_per_request": total_tokens / total_requests if total_requests > 0 else 0,
            "recent_requests": len(recent_usage)
        }
    
    def suggest_optimizations(self) -> List[str]:
        """
        Suggest optimizations based on usage patterns.
        
        Returns:
            List of optimization suggestions
        """
        suggestions = []
        stats = self.get_usage_stats()
        
        if stats["session_usage"] > 0.8:
            suggestions.append("Consider starting a new session to reset token usage")
        
        if stats["hourly_usage"] > 0.8:
            suggestions.append("Approaching hourly token limit - consider reducing request frequency")
        
        if stats["average_tokens_per_request"] > 4000:
            suggestions.append("Average request size is large - consider breaking down complex requests")
        
        if len(self.usage_history) > 50:
            recent_failures = sum(1 for usage in self.usage_history[-20:] if usage.total_tokens > 6000)
            if recent_failures > 5:
                suggestions.append("Many recent large requests - consider using context compression")
        
        return suggestions
    
    def _truncate_oldest(self, messages: List[Any], target_tokens: int) -> List[Any]:
        """Truncate oldest messages to fit target."""
        result = messages.copy()
        current_tokens = sum(self.estimate_tokens(str(msg)) for msg in result)
        
        while current_tokens > target_tokens and len(result) > 1:
            # Keep the last message (usually the user's current request)
            result.pop(0)
            current_tokens = sum(self.estimate_tokens(str(msg)) for msg in result)
        
        return result
    
    def _summarize_context(self, messages: List[Any], target_tokens: int) -> List[Any]:
        """Summarize older context to reduce tokens."""
        if len(messages) <= 2:
            return messages
        
        # Keep the last 2 messages, summarize the rest
        recent_messages = messages[-2:]
        older_messages = messages[:-2]
        
        # Create a summary of older messages
        summary_text = f"[Previous conversation summary: {len(older_messages)} messages exchanged]"
        
        # Create a summary message
        summary_msg = type(messages[0])(
            role="system",
            content=summary_text
        ) if hasattr(messages[0], 'role') else summary_text
        
        return [summary_msg] + recent_messages
    
    def _smart_compression(self, messages: List[Any], target_tokens: int) -> List[Any]:
        """Apply smart compression techniques."""
        result = []
        remaining_tokens = target_tokens
        
        # Process messages in reverse order (keep recent ones)
        for msg in reversed(messages):
            msg_tokens = self.estimate_tokens(str(msg))
            
            if msg_tokens <= remaining_tokens:
                result.insert(0, msg)
                remaining_tokens -= msg_tokens
            elif remaining_tokens > 100:  # If we have some space left
                # Compress the message
                compressed = self._compress_message(msg, remaining_tokens)
                if compressed:
                    result.insert(0, compressed)
                    remaining_tokens -= self.estimate_tokens(str(compressed))
                break
            else:
                break
        
        return result
    
    def _priority_based_optimization(self, messages: List[Any], target_tokens: int) -> List[Any]:
        """Optimize based on message priority."""
        # Assign priorities (higher = more important)
        prioritized = []
        for i, msg in enumerate(messages):
            priority = 1.0
            
            # Recent messages have higher priority
            if i >= len(messages) - 3:
                priority += 2.0
            
            # System messages have higher priority
            if hasattr(msg, 'role') and msg.role == 'system':
                priority += 1.0
            
            # Tool responses have medium priority
            if hasattr(msg, 'role') and msg.role == 'tool':
                priority += 0.5
            
            prioritized.append((priority, msg))
        
        # Sort by priority (descending)
        prioritized.sort(key=lambda x: x[0], reverse=True)
        
        # Select messages within token budget
        result = []
        used_tokens = 0
        
        for priority, msg in prioritized:
            msg_tokens = self.estimate_tokens(str(msg))
            if used_tokens + msg_tokens <= target_tokens:
                result.append(msg)
                used_tokens += msg_tokens
        
        # Restore original order
        original_order = []
        for msg in messages:
            if msg in result:
                original_order.append(msg)
        
        return original_order
    
    def _compress_message(self, message: Any, max_tokens: int) -> Any:
        """Compress a single message to fit within token limit."""
        if not hasattr(message, 'content'):
            return message
        
        content = str(message.content)
        target_length = max_tokens * 4  # Rough character estimate
        
        if len(content) <= target_length:
            return message
        
        # Simple compression: take first and last parts
        first_part = content[:target_length//2]
        last_part = content[-target_length//2:]
        compressed_content = f"{first_part}...[truncated]...{last_part}"
        
        # Create compressed message
        if hasattr(message, 'replace'):
            return message.replace(content=compressed_content)
        else:
            return compressed_content

# Global token manager instance
_token_manager: Optional[TokenManager] = None

def get_token_manager() -> TokenManager:
    """Get the global token manager instance."""
    global _token_manager
    if _token_manager is None:
        _token_manager = TokenManager()
    return _token_manager
