"""
Interrupt handling utilities.

This module provides utilities for handling keyboard interrupts
and managing interruptible operations.
"""

import signal
import threading
from typing import Optional, Callable

# Global state for interrupt handling
_interruptible = threading.local()
_interrupt_handler: Optional[Callable] = None


def set_interruptible() -> None:
    """Mark the current thread as interruptible."""
    _interruptible.enabled = True


def clear_interruptible() -> None:
    """Mark the current thread as not interruptible."""
    _interruptible.enabled = False


def is_interruptible() -> bool:
    """Check if the current thread is interruptible."""
    return getattr(_interruptible, 'enabled', False)


def handle_interrupt(signum: int, frame) -> None:
    """
    Handle keyboard interrupt signal.
    
    Args:
        signum: Signal number
        frame: Current stack frame
    """
    if is_interruptible():
        if _interrupt_handler:
            _interrupt_handler()
        else:
            raise KeyboardInterrupt("Operation interrupted by user")


def set_interrupt_handler(handler: Callable) -> None:
    """
    Set a custom interrupt handler.
    
    Args:
        handler: Function to call on interrupt
    """
    global _interrupt_handler
    _interrupt_handler = handler


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful interruption."""
    signal.signal(signal.SIGINT, handle_interrupt)
    
    # Handle SIGTERM on Unix systems
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, handle_interrupt)


class InterruptibleContext:
    """Context manager for interruptible operations."""
    
    def __enter__(self):
        """Enter the interruptible context."""
        set_interruptible()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the interruptible context."""
        clear_interruptible()


def interruptible(func: Callable) -> Callable:
    """
    Decorator to make a function interruptible.
    
    Args:
        func: Function to make interruptible
    
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        with InterruptibleContext():
            return func(*args, **kwargs)
    
    return wrapper


__all__ = [
    "set_interruptible",
    "clear_interruptible", 
    "is_interruptible",
    "handle_interrupt",
    "set_interrupt_handler",
    "setup_signal_handlers",
    "InterruptibleContext",
    "interruptible",
]
