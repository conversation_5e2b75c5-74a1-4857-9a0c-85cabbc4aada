"""
GitHub integration tool.

This tool provides GitHub repository management and interaction capabilities
including cloning, committing, pushing, and repository analysis.
"""

import logging
import os
import subprocess
import json
from typing import Generator, Dict, Any
from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)

class GitHubTool(ToolSpec):
    """Advanced GitHub integration tool."""

    def __init__(self):
        super().__init__(
            name="github",
            description="Interact with GitHub repositories, manage code, and perform Git operations",
            parameters=[
                Parameter("action", "str", "Action: clone, status, add, commit, push, pull, branch, log", required=True),
                Parameter("repo", "str", "Repository URL or name", required=False),
                Parameter("message", "str", "Commit message", required=False),
                Parameter("branch", "str", "Branch name", required=False),
                Parameter("file", "str", "File path to add", required=False),
                Parameter("path", "str", "Local repository path", required=False)
            ],
            block_types=["github", "git", "repository", "repo"]
        )

    def is_available(self) -> bool:
        """Check if Git is available."""
        try:
            result = subprocess.run(
                ["git", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute GitHub/Git operations."""
        try:
            action_info = self._parse_git_command(content)
            action = action_info.get("action", "status")

            if action == "clone":
                yield from self._clone_repo(action_info)
            elif action == "status":
                yield from self._git_status(action_info)
            elif action == "add":
                yield from self._git_add(action_info)
            elif action == "commit":
                yield from self._git_commit(action_info)
            elif action == "push":
                yield from self._git_push(action_info)
            elif action == "pull":
                yield from self._git_pull(action_info)
            elif action == "branch":
                yield from self._git_branch(action_info)
            elif action == "log":
                yield from self._git_log(action_info)
            elif action == "init":
                yield from self._git_init(action_info)
            elif action == "remote":
                yield from self._git_remote(action_info)
            else:
                yield self.create_response(f"Unknown git action: {action}")

        except Exception as e:
            yield self.create_response(f"Git operation error: {str(e)}")

    def _parse_git_command(self, content: str) -> Dict[str, Any]:
        """Parse git command from content."""
        lines = content.strip().split('\n')
        action_info = {"action": "status"}

        for line in lines:
            line = line.strip()
            if line.startswith('action:'):
                action_info["action"] = line.split(':', 1)[1].strip()
            elif line.startswith('repo:'):
                action_info["repo"] = line.split(':', 1)[1].strip()
            elif line.startswith('message:'):
                action_info["message"] = line.split(':', 1)[1].strip()
            elif line.startswith('branch:'):
                action_info["branch"] = line.split(':', 1)[1].strip()
            elif line.startswith('file:'):
                action_info["file"] = line.split(':', 1)[1].strip()
            elif line.startswith('path:'):
                action_info["path"] = line.split(':', 1)[1].strip()
            elif not line.startswith(('action:', 'repo:', 'message:', 'branch:', 'file:', 'path:')) and line:
                # Treat as repo URL if it looks like one
                if 'github.com' in line or line.endswith('.git'):
                    action_info["repo"] = line
                elif action_info.get("action") == "commit":
                    action_info["message"] = line

        return action_info

    def _clone_repo(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Clone a GitHub repository."""
        repo = action_info.get("repo", "")
        path = action_info.get("path", "")

        if not repo:
            yield self.create_response("Error: No repository URL provided")
            return

        try:
            cmd = ["git", "clone", repo]
            if path:
                cmd.append(path)

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                output = f"Successfully cloned repository: {repo}\n"
                if result.stdout:
                    output += f"Output: {result.stdout}"
                yield self.create_response(output)
            else:
                error_msg = f"Failed to clone repository: {repo}\n"
                if result.stderr:
                    error_msg += f"Error: {result.stderr}"
                yield self.create_response(error_msg)

        except subprocess.TimeoutExpired:
            yield self.create_response("Clone operation timed out")
        except Exception as e:
            yield self.create_response(f"Clone error: {str(e)}")

    def _git_status(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Get git status."""
        path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                if result.stdout.strip():
                    output = "Git Status (modified files):\n" + result.stdout
                else:
                    output = "Working directory is clean"

                # Also get branch info
                branch_result = subprocess.run(
                    ["git", "branch", "--show-current"],
                    cwd=path,
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if branch_result.returncode == 0 and branch_result.stdout.strip():
                    output = f"Current branch: {branch_result.stdout.strip()}\n" + output

                yield self.create_response(output)
            else:
                yield self.create_response(f"Git status error: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Status error: {str(e)}")

    def _git_add(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Add files to git staging."""
        file_path = action_info.get("file", ".")
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "add", file_path],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                yield self.create_response(f"Added to staging: {file_path}")
            else:
                yield self.create_response(f"Git add error: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Add error: {str(e)}")

    def _git_commit(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Commit changes."""
        message = action_info.get("message", "Automated commit")
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "commit", "-m", message],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                output = f"Committed with message: '{message}'\n"
                if result.stdout:
                    output += result.stdout
                yield self.create_response(output)
            else:
                error_msg = f"Commit failed: {result.stderr}"
                yield self.create_response(error_msg)

        except Exception as e:
            yield self.create_response(f"Commit error: {str(e)}")

    def _git_push(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Push changes to remote repository."""
        branch = action_info.get("branch", "")
        repo_path = action_info.get("path", ".")

        try:
            cmd = ["git", "push"]
            if branch:
                cmd.extend(["origin", branch])

            result = subprocess.run(
                cmd,
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                output = "Successfully pushed to remote repository\n"
                if result.stdout:
                    output += result.stdout
                yield self.create_response(output)
            else:
                error_msg = f"Push failed: {result.stderr}"
                yield self.create_response(error_msg)

        except Exception as e:
            yield self.create_response(f"Push error: {str(e)}")

    def _git_pull(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Pull changes from remote repository."""
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "pull"],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                output = "Successfully pulled from remote repository\n"
                if result.stdout:
                    output += result.stdout
                yield self.create_response(output)
            else:
                error_msg = f"Pull failed: {result.stderr}"
                yield self.create_response(error_msg)

        except Exception as e:
            yield self.create_response(f"Pull error: {str(e)}")

    def _git_branch(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Manage git branches."""
        branch = action_info.get("branch", "")
        repo_path = action_info.get("path", ".")

        try:
            if branch:
                # Create and switch to new branch
                result = subprocess.run(
                    ["git", "checkout", "-b", branch],
                    cwd=repo_path,
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    yield self.create_response(f"Created and switched to branch: {branch}")
                else:
                    yield self.create_response(f"Branch creation failed: {result.stderr}")
            else:
                # List branches
                result = subprocess.run(
                    ["git", "branch", "-a"],
                    cwd=repo_path,
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    output = "Git branches:\n" + result.stdout
                    yield self.create_response(output)
                else:
                    yield self.create_response(f"Branch listing failed: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Branch error: {str(e)}")

    def _git_log(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Show git commit log."""
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "log", "--oneline", "-10"],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                if result.stdout.strip():
                    output = "Recent commits:\n" + result.stdout
                else:
                    output = "No commits found"
                yield self.create_response(output)
            else:
                yield self.create_response(f"Log error: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Log error: {str(e)}")

    def _git_init(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Initialize a new git repository."""
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "init"],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                yield self.create_response(f"Initialized git repository in: {repo_path}")
            else:
                yield self.create_response(f"Git init failed: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Init error: {str(e)}")

    def _git_remote(self, action_info: Dict[str, Any]) -> Generator[Message, None, None]:
        """Manage git remotes."""
        repo_path = action_info.get("path", ".")

        try:
            result = subprocess.run(
                ["git", "remote", "-v"],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                if result.stdout.strip():
                    output = "Git remotes:\n" + result.stdout
                else:
                    output = "No remotes configured"
                yield self.create_response(output)
            else:
                yield self.create_response(f"Remote error: {result.stderr}")

        except Exception as e:
            yield self.create_response(f"Remote error: {str(e)}")

# Create tool instance
github_tool = GitHubTool()
