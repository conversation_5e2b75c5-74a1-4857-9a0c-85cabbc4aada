"""
Logging setup utilities.

This module provides logging configuration for the AI agent.
"""

import logging
import sys
import warnings
from typing import Optional


def setup_logging(verbose: bool = False, log_file: Optional[str] = None, disable_console_logs: bool = True) -> None:
    """
    Setup logging configuration - completely disables all logging and warnings.

    Args:
        verbose: Enable verbose logging (ignored - always disabled)
        log_file: Optional log file path (ignored - no file logging)
        disable_console_logs: Disable all console logging (always True)
    """
    # Completely disable all logging by setting to highest level
    log_level = logging.CRITICAL + 1  # Higher than CRITICAL to disable everything

    # Setup root logger to disable everything
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.disabled = True

    # Clear all existing handlers
    root_logger.handlers.clear()

    # Disable all external library loggers completely
    external_loggers = [
        "urllib3", "requests", "httpx", "google", "googleapiclient",
        "google.auth", "google.generativeai", "werkzeug", "flask",
        "asyncio", "concurrent", "threading", "multiprocessing",
        "aiagent", "tools", "llm", "chat", "conversation", "workflow"
    ]

    for logger_name in external_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL + 1)
        logger.disabled = True
        logger.handlers.clear()
        logger.propagate = False

    # Disable warnings module
    import warnings
    warnings.filterwarnings("ignore")
    warnings.simplefilter("ignore")

    # Suppress all Python warnings
    import sys
    if not sys.warnoptions:
        warnings.simplefilter("ignore")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    return logging.getLogger(name)


__all__ = [
    "setup_logging",
    "get_logger",
]
