"""
Logging setup utilities.

This module provides logging configuration for the AI agent.
"""

import logging
import sys
from typing import Optional
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.console import Console

console = Console()


def setup_logging(verbose: bool = False, log_file: Optional[str] = None, disable_console_logs: bool = False) -> None:
    """
    Setup logging configuration.

    Args:
        verbose: Enable verbose logging
        log_file: Optional log file path
        disable_console_logs: Disable all console logging
    """
    # Set log level
    log_level = logging.CRITICAL

    # Create formatters
    rich_formatter = logging.Formatter(
        fmt="%(message)s",
        datefmt="[%X]"
    )

    file_formatter = logging.Formatter(
        fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Clear existing handlers
    root_logger.handlers.clear()

    # Add rich handler for console output if not disabled
    if not disable_console_logs:
        rich_handler = RichHandler(
            console=console,
            show_time=verbose,
            show_path=verbose,
            markup=True,
            rich_tracebacks=True
        )
        rich_handler.setLevel(log_level)
        rich_handler.setFormatter(rich_formatter)
        root_logger.addHandler(rich_handler)

    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

    # Set specific logger levels
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)

    if not verbose and not disable_console_logs:
        # Reduce noise from external libraries
        logging.getLogger("google").setLevel(logging.WARNING)
        logging.getLogger("googleapiclient").setLevel(logging.WARNING)
        logging.getLogger("google.auth").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    return logging.getLogger(name)


__all__ = [
    "setup_logging",
    "get_logger",
]
