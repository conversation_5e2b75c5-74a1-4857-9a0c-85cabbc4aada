"""
Tools system for the AI agent.

This module provides a comprehensive tool system that allows the AI agent
to interact with the environment, execute code, browse the web, and more.
"""

import logging
import threading
from typing import List, Dict, Any, Optional, Generator, Callable
from enum import Enum

from tools.base import ToolSpec, ToolUse, Parameter, ConfirmFunc
from tools.registry import ToolRegistry
from message import Message
from config import get_config

logger = logging.getLogger(__name__)


class ToolFormat(Enum):
    """Tool invocation formats."""
    MARKDOWN = "markdown"
    XML = "xml"
    FUNCTION = "function"


# Global tool format
_tool_format: ToolFormat = ToolFormat.MARKDOWN

# Thread-local storage for tools
_thread_local = threading.local()


def get_tool_format() -> ToolFormat:
    """Get the current tool format."""
    return _tool_format


def set_tool_format(format: ToolFormat) -> None:
    """Set the tool format."""
    global _tool_format
    _tool_format = format


def _get_registry() -> ToolRegistry:
    """Get the thread-local tool registry."""
    if not hasattr(_thread_local, "registry"):
        _thread_local.registry = ToolRegistry()
    return _thread_local.registry


def init_tools(allowlist: Optional[List[str]] = None) -> List[ToolSpec]:
    """
    Initialize tools with optional allowlist.
    
    Args:
        allowlist: List of tool names to allow (None for all available)
    
    Returns:
        List of initialized tools
    """
    registry = _get_registry()
    config = get_config()
    
    # Use allowlist from config if not provided
    if allowlist is None:
        allowlist = config.tools.enabled
    
    # Discover and register tools
    registry.discover_tools()
    
    # Initialize allowed tools
    initialized_tools = []
    for tool_name in allowlist:
        try:
            tool = registry.get_tool(tool_name)
            if tool and tool.is_available():
                if tool.init:
                    tool = tool.init()
                initialized_tools.append(tool)
                logger.debug(f"Initialized tool: {tool_name}")
            else:
                logger.warning(f"Tool '{tool_name}' is not available")
        except Exception as e:
            logger.error(f"Failed to get tool '{tool_name}': {e}")
    
    return initialized_tools


def get_tools() -> List[ToolSpec]:
    """Get all initialized tools."""
    registry = _get_registry()
    return registry.get_initialized_tools()


def get_tool(name: str) -> Optional[ToolSpec]:
    """Get a tool by name."""
    registry = _get_registry()
    return registry.get_tool(name)


def has_tool(name: str) -> bool:
    """Check if a tool is available."""
    return get_tool(name) is not None


def list_available_tools() -> List[str]:
    """List all available tool names."""
    registry = _get_registry()
    return registry.list_available_tools()


def _detect_implicit_tool_uses(content: str) -> List[ToolUse]:
    """
    Intelligently detect tool usage from natural language content.

    Args:
        content: Message content to analyze

    Returns:
        List of detected tool uses
    """
    tool_uses = []
    content_lower = content.lower()

    # Detect common patterns that suggest tool usage
    patterns = {
        'shell': ['run command', 'execute command', 'terminal', 'bash', 'cmd', 'shell'],
        'python': ['run python', 'execute python', 'python code', 'python script'],
        'file': ['read file', 'write file', 'create file', 'edit file', 'file operation'],
        'web': ['search web', 'web search', 'google search', 'fetch url', 'web scraping'],
        'browser': ['open browser', 'browse to', 'navigate to', 'web automation'],
        'github': ['github', 'git clone', 'repository', 'commit', 'push', 'pull'],
        'codebase': ['analyze code', 'code analysis', 'codebase', 'code review']
    }

    for tool_name, keywords in patterns.items():
        if any(keyword in content_lower for keyword in keywords):
            # Create a basic tool use suggestion
            tool_use = ToolUse(
                tool=tool_name,
                content=content,
                call_id=f"implicit_{tool_name}_{len(tool_uses)}"
            )
            tool_uses.append(tool_use)

    return tool_uses


def execute_msg(
    message: Message,
    confirm: ConfirmFunc
) -> Generator[Message, None, None]:
    """
    Execute tools in a message and yield responses with enhanced intelligence.

    Args:
        message: Message containing tool calls
        confirm: Confirmation function for tool execution

    Yields:
        Response messages from tool execution
    """
    if message.role != "assistant":
        logger.warning("Only assistant messages can contain tool calls")
        return

    # Extract tool uses from message content
    tool_uses = list(ToolUse.iter_from_content(message.content))

    # If no explicit tool uses found, try to intelligently detect tool needs
    if not tool_uses:
        tool_uses = _detect_implicit_tool_uses(message.content)

    execution_count = 0
    for tool_use in tool_uses:
        if not tool_use.is_runnable:
            continue

        execution_count += 1

        try:
            # Get the tool
            tool = get_tool(tool_use.tool)
            if not tool:
                # Try to find similar tool or suggest alternatives
                available_tools = list_available_tools()
                similar_tools = [t for t in available_tools if tool_use.tool.lower() in t.lower() or t.lower() in tool_use.tool.lower()]

                if similar_tools:
                    yield Message(
                        role="system",
                        content=f"Tool '{tool_use.tool}' not found. Similar tools available: {', '.join(similar_tools)}",
                        call_id=tool_use.call_id
                    )
                else:
                    yield Message(
                        role="system",
                        content=f"Error: Tool '{tool_use.tool}' not found. Available tools: {', '.join(available_tools)}",
                        call_id=tool_use.call_id
                    )
                continue

            # Enhanced confirmation with autonomous mode support
            should_execute = True
            if hasattr(confirm, '__call__'):
                # For autonomous operation, auto-confirm most operations
                config = get_config()
                if config.tools.auto_confirm:
                    should_execute = True
                else:
                    should_execute = confirm(f"Execute {tool_use.tool} with: {tool_use.content[:100]}...")

            if not should_execute:
                yield Message(
                    role="system",
                    content=f"Tool execution cancelled: {tool_use.tool}",
                    call_id=tool_use.call_id
                )
                continue

            # Execute the tool with enhanced error handling
            logger.info(f"Executing tool: {tool_use.tool} (execution #{execution_count})")

            response_count = 0
            for response in tool.execute(tool_use.content, **tool_use.kwargs):
                response_count += 1
                enhanced_response = response.replace(
                    call_id=tool_use.call_id,
                    metadata={
                        **response.metadata,
                        "tool_name": tool_use.tool,
                        "execution_number": execution_count,
                        "response_number": response_count,
                        "autonomous_execution": config.tools.auto_confirm if 'config' in locals() else False
                    }
                )
                yield enhanced_response

        except KeyboardInterrupt:
            yield Message(
                role="system",
                content="Tool execution interrupted by user",
                call_id=tool_use.call_id
            )
            break
        except Exception as e:
            # Enhanced error handling with recovery suggestions
            error_msg = f"Error executing {tool_use.tool}: {str(e)}"
            if "permission" in str(e).lower():
                error_msg += " (Try running with appropriate permissions)"
            elif "not found" in str(e).lower():
                error_msg += " (Check if required dependencies are installed)"
            elif "timeout" in str(e).lower():
                error_msg += " (Operation timed out, try with smaller scope)"

            yield Message(
                role="system",
                content=error_msg,
                call_id=tool_use.call_id,
                metadata={"error": True, "tool": tool_use.tool}
            )
            continue

    # If no tools were executed, provide helpful feedback
    if execution_count == 0 and tool_uses:
        yield Message(
            role="system",
            content="No tools were executed. Check tool syntax and availability.",
            metadata={"no_execution": True}
        )


def get_tool_for_language(language: str) -> Optional[ToolSpec]:
    """Get the tool that handles a specific language/block type."""
    for tool in get_tools():
        if language in tool.block_types:
            return tool
    return None


def is_supported_language(language: str) -> bool:
    """Check if a language/block type is supported."""
    return get_tool_for_language(language) is not None


def clear_tools() -> None:
    """Clear all tools (useful for testing)."""
    if hasattr(_thread_local, "registry"):
        _thread_local.registry.clear()


def get_tools_for_llm() -> List[Dict[str, Any]]:
    """
    Get tools formatted for LLM function calling with enhanced descriptions.

    Returns:
        List of tool definitions optimized for AI agent usage
    """
    tools = []

    for tool in get_tools():
        if not tool.is_runnable:
            continue

        # Enhanced tool description for better AI understanding
        enhanced_description = f"{tool.description}\n\nUsage: Use this tool when you need to {tool.name}. "

        # Add usage examples based on tool type
        if tool.name == "shell":
            enhanced_description += "Execute system commands, run scripts, manage processes. Example: ```shell\nls -la\n```"
        elif tool.name == "python":
            enhanced_description += "Run Python code, data analysis, calculations. Example: ```python\nprint('Hello World')\n```"
        elif tool.name == "file":
            enhanced_description += "Read, write, create, edit files. Example: ```file\noperation: read\npath: example.txt\n```"
        elif tool.name == "web":
            enhanced_description += "Search web, fetch URLs, scrape content. Example: ```web\naction: search\nquery: Python tutorials\n```"
        elif tool.name == "browser":
            enhanced_description += "Browse websites, automate web interactions. Example: ```browser\naction: navigate\nurl: https://example.com\n```"
        elif tool.name == "github":
            enhanced_description += "Git operations, repository management. Example: ```github\naction: clone\nrepo: https://github.com/user/repo.git\n```"
        elif tool.name == "computer":
            enhanced_description += "Desktop automation, screenshots, system info. Example: ```computer\naction: screenshot\n```"
        elif tool.name == "rag":
            enhanced_description += "Document search, indexing, context retrieval. Example: ```rag\naction: search\nquery: machine learning\n```"
        elif tool.name == "codebase":
            enhanced_description += "Code analysis, project structure, refactoring. Example: ```codebase\naction: analyze\npath: ./src\n```"

        # Convert to function calling format
        function_def = {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": enhanced_description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": f"Tool content in markdown code block format for {tool.name}"
                        }
                    },
                    "required": ["content"]
                }
            }
        }

        # Add specific parameters if available
        if hasattr(tool, 'parameters') and tool.parameters:
            for param in tool.parameters:
                param_type = param.type
                if param_type == "str":
                    param_type = "string"
                elif param_type == "int":
                    param_type = "integer"
                elif param_type == "float":
                    param_type = "number"
                elif param_type == "bool":
                    param_type = "boolean"

                function_def["function"]["parameters"]["properties"][param.name] = {
                    "type": param_type,
                    "description": param.description
                }

                if hasattr(param, 'enum') and param.enum:
                    function_def["function"]["parameters"]["properties"][param.name]["enum"] = param.enum

                if param.required and param.name != "content":
                    function_def["function"]["parameters"]["required"].append(param.name)

        tools.append(function_def)

    return tools


def execute_tool_chain(tools_sequence: List[Dict[str, Any]], confirm: ConfirmFunc) -> Generator[Message, None, None]:
    """
    Execute a sequence of tools in chain for complex workflows.

    Args:
        tools_sequence: List of tool execution requests
        confirm: Confirmation function

    Yields:
        Response messages from tool chain execution
    """
    try:
        for i, tool_request in enumerate(tools_sequence):
            tool_name = tool_request.get("tool")
            content = tool_request.get("content", "")

            yield Message(
                role="system",
                content=f"Executing tool {i+1}/{len(tools_sequence)}: {tool_name}",
                metadata={"chain_step": i+1, "total_steps": len(tools_sequence)}
            )

            # Create a mock message for tool execution
            mock_message = Message(
                role="assistant",
                content=f"```{tool_name}\n{content}\n```"
            )

            # Execute the tool
            for response in execute_msg(mock_message, confirm):
                yield response

    except Exception as e:
        yield Message(
            role="system",
            content=f"Tool chain execution error: {str(e)}",
            metadata={"error": True, "chain_execution": True}
        )


def get_tool_suggestions(user_input: str) -> List[str]:
    """
    Suggest appropriate tools based on user input.

    Args:
        user_input: User's request or query

    Returns:
        List of suggested tool names
    """
    suggestions = []
    input_lower = user_input.lower()

    # Tool suggestion patterns
    patterns = {
        "shell": ["command", "terminal", "bash", "cmd", "execute", "run command"],
        "python": ["python", "code", "script", "calculate", "data", "analysis"],
        "file": ["file", "read", "write", "create", "edit", "save", "open"],
        "web": ["search", "google", "web", "internet", "find", "lookup"],
        "browser": ["browse", "website", "url", "navigate", "scrape"],
        "github": ["git", "github", "repository", "clone", "commit", "push"],
        "computer": ["screenshot", "desktop", "automation", "system"],
        "rag": ["document", "search document", "index", "retrieve"],
        "codebase": ["code analysis", "project", "codebase", "refactor"]
    }

    for tool_name, keywords in patterns.items():
        if any(keyword in input_lower for keyword in keywords):
            suggestions.append(tool_name)

    return suggestions


# Import all tool modules and register them explicitly
def _register_all_tools():
    """Register all available tools."""
    registry = _get_registry()

    # Import and register tools
    try:
        from tools.shell import shell_tool
        registry.register_tool(shell_tool)
    except ImportError:
        pass

    try:
        from tools.python import python_tool
        registry.register_tool(python_tool)
    except ImportError as e:
        print(f"Failed to import code tool: {e}")
        pass
    except Exception as e:
        print(f"Error registering code tool: {e}")
        pass

    try:
        from tools.file_ops import file_tool
        registry.register_tool(file_tool)
    except ImportError:
        pass

    try:
        from tools.web_tools import web_tools
        registry.register_tool(web_tools)
    except ImportError:
        pass

    try:
        from tools.browser import browser_tool
        registry.register_tool(browser_tool)
    except ImportError:
        pass

    try:
        from tools.computer import computer_tool
        registry.register_tool(computer_tool)
    except ImportError:
        pass

    try:
        from tools.github_tool import github_tool
        registry.register_tool(github_tool)
    except ImportError:
        pass

    try:
        from tools.rag import rag_tool
        registry.register_tool(rag_tool)
    except ImportError:
        pass

    try:
        from tools.codebase_tools import codebase_tool
        registry.register_tool(codebase_tool)
    except ImportError:
        pass

# Register all tools
_register_all_tools()

__all__ = [
    # Core classes
    "ToolSpec",
    "ToolUse", 
    "Parameter",
    "ConfirmFunc",
    "ToolFormat",
    "ToolRegistry",
    
    # Functions
    "get_tool_format",
    "set_tool_format",
    "init_tools",
    "get_tools",
    "get_tool",
    "has_tool",
    "list_available_tools",
    "execute_msg",
    "get_tool_for_language",
    "is_supported_language",
    "clear_tools",
    "get_tools_for_llm",
]
