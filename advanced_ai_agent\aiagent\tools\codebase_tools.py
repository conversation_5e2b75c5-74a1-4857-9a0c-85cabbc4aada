"""
Advanced codebase analysis and management tools.

This tool provides comprehensive codebase analysis, indexing, search,
and intelligent code manipulation capabilities.
"""

import os
import re
import ast
import json
import logging
import subprocess
from typing import Generator, Dict, Any, List, Optional, Set
from pathlib import Path
from collections import defaultdict

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


class CodebaseToolsSpec(ToolSpec):
    """Advanced codebase analysis and management tools."""
    
    def __init__(self):
        super().__init__(
            name="codebase",
            description="Analyze, index, search, and manipulate codebases with intelligent features",
            parameters=[
                Parameter(
                    name="action",
                    type="string",
                    description="Codebase action to perform",
                    required=True,
                    enum=["index", "search", "analyze", "grep", "find", "dependencies", "refactor", "metrics"]
                ),
                Parameter(
                    name="path",
                    type="string",
                    description="Path to analyze (file or directory)",
                    required=False,
                    default="."
                ),
                Parameter(
                    name="query",
                    type="string",
                    description="Search query or pattern",
                    required=False
                ),
                Parameter(
                    name="language",
                    type="string",
                    description="Programming language filter",
                    required=False,
                    enum=["python", "javascript", "typescript", "java", "cpp", "c", "go", "rust", "php", "ruby"]
                ),
                Parameter(
                    name="include_patterns",
                    type="string",
                    description="File patterns to include (comma-separated)",
                    required=False
                ),
                Parameter(
                    name="exclude_patterns",
                    type="string",
                    description="File patterns to exclude (comma-separated)",
                    required=False,
                    default=".git,node_modules,__pycache__,.venv,dist,build"
                ),
                Parameter(
                    name="max_depth",
                    type="integer",
                    description="Maximum directory depth to search",
                    required=False,
                    default=10
                ),
                Parameter(
                    name="case_sensitive",
                    type="boolean",
                    description="Case sensitive search",
                    required=False,
                    default=False
                )
            ],
            block_types=["codebase", "code", "search", "grep", "analyze"]
        )
        self._index_cache = {}
        self._language_extensions = {
            "python": [".py", ".pyx", ".pyi"],
            "javascript": [".js", ".jsx", ".mjs"],
            "typescript": [".ts", ".tsx"],
            "java": [".java"],
            "cpp": [".cpp", ".cxx", ".cc", ".hpp", ".h"],
            "c": [".c", ".h"],
            "go": [".go"],
            "rust": [".rs"],
            "php": [".php"],
            "ruby": [".rb"]
        }
    
    def is_available(self) -> bool:
        """Check if codebase tools are available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute codebase operation.
        
        Args:
            content: Operation content
            **kwargs: Additional parameters
        
        Yields:
            Response messages with operation results
        """
        try:
            # Parse content if action not specified
            if "action" not in kwargs:
                action, path, query = self._parse_content(content)
                kwargs.update({
                    "action": action,
                    "path": path,
                    "query": query
                })
            
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            # Execute action
            if action == "index":
                yield from self._index_codebase(params)
            elif action == "search":
                yield from self._search_code(params)
            elif action == "analyze":
                yield from self._analyze_codebase(params)
            elif action == "grep":
                yield from self._grep_files(params)
            elif action == "find":
                yield from self._find_files(params)
            elif action == "dependencies":
                yield from self._analyze_dependencies(params)
            elif action == "refactor":
                yield from self._suggest_refactoring(params)
            elif action == "metrics":
                yield from self._calculate_metrics(params)
            else:
                yield self.create_response(f"Unknown codebase action: {action}")
                
        except Exception as e:
            logger.error(f"Error in codebase operation: {e}")
            yield self.create_response(self.format_error(e))
    
    def _parse_content(self, content: str) -> tuple[str, str, str]:
        """Parse content to extract action, path, and query."""
        lines = content.strip().split('\n')
        
        if not lines:
            return "analyze", ".", ""
        
        first_line = lines[0].strip()
        parts = first_line.split(None, 2)
        
        if len(parts) >= 1:
            action = parts[0].lower()
            path = parts[1] if len(parts) > 1 else "."
            query = parts[2] if len(parts) > 2 else ""
        else:
            action = "analyze"
            path = "."
            query = first_line
        
        return action, path, query
    
    def _get_file_list(self, path: str, params: Dict[str, Any]) -> List[Path]:
        """Get list of files to process based on parameters."""
        base_path = Path(path)
        
        if not base_path.exists():
            return []
        
        if base_path.is_file():
            return [base_path]
        
        # Get parameters
        language = params.get("language")
        include_patterns = params.get("include_patterns", "").split(",") if params.get("include_patterns") else []
        exclude_patterns = params.get("exclude_patterns", "").split(",")
        max_depth = params.get("max_depth", 10)
        
        # Get file extensions for language
        extensions = set()
        if language and language in self._language_extensions:
            extensions.update(self._language_extensions[language])
        
        files = []
        
        def should_exclude(file_path: Path) -> bool:
            """Check if file should be excluded."""
            path_str = str(file_path)
            for pattern in exclude_patterns:
                if pattern.strip() and pattern.strip() in path_str:
                    return True
            return False
        
        def should_include(file_path: Path) -> bool:
            """Check if file should be included."""
            if include_patterns:
                path_str = str(file_path)
                return any(pattern.strip() in path_str for pattern in include_patterns if pattern.strip())
            return True
        
        # Walk directory tree
        for root, dirs, filenames in os.walk(base_path):
            root_path = Path(root)
            
            # Check depth
            depth = len(root_path.relative_to(base_path).parts)
            if depth > max_depth:
                continue
            
            # Filter directories
            dirs[:] = [d for d in dirs if not should_exclude(root_path / d)]
            
            # Process files
            for filename in filenames:
                file_path = root_path / filename
                
                # Skip excluded files
                if should_exclude(file_path):
                    continue
                
                # Check include patterns
                if not should_include(file_path):
                    continue
                
                # Check language extensions
                if extensions and file_path.suffix not in extensions:
                    continue
                
                # Skip binary files
                if self._is_binary_file(file_path):
                    continue
                
                files.append(file_path)
        
        return files
    
    def _is_binary_file(self, file_path: Path) -> bool:
        """Check if file is binary."""
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\0' in chunk
        except:
            return True
    
    def _index_codebase(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Index the codebase for fast searching."""
        path = params.get("path", ".")
        
        yield self.create_response(f"Indexing codebase at: {path}")
        
        files = self._get_file_list(path, params)
        
        index = {
            "files": {},
            "functions": {},
            "classes": {},
            "imports": {},
            "symbols": {},
            "statistics": {}
        }
        
        total_files = len(files)
        processed = 0
        
        for file_path in files:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Analyze file
                file_info = self._analyze_file(file_path, content)
                index["files"][str(file_path)] = file_info
                
                # Update symbol indices
                for func in file_info.get("functions", []):
                    index["functions"][func["name"]] = {
                        "file": str(file_path),
                        "line": func["line"],
                        "signature": func.get("signature", "")
                    }
                
                for cls in file_info.get("classes", []):
                    index["classes"][cls["name"]] = {
                        "file": str(file_path),
                        "line": cls["line"],
                        "methods": cls.get("methods", [])
                    }
                
                processed += 1
                
                # Progress update
                if processed % 50 == 0:
                    yield self.create_response(f"Processed {processed}/{total_files} files...")
                
            except Exception as e:
                logger.warning(f"Error indexing {file_path}: {e}")
        
        # Calculate statistics
        index["statistics"] = {
            "total_files": total_files,
            "total_functions": len(index["functions"]),
            "total_classes": len(index["classes"]),
            "languages": self._detect_languages(files)
        }
        
        # Cache index
        self._index_cache[path] = index
        
        yield self.create_response(
            f"Indexing complete!\n"
            f"Files: {index['statistics']['total_files']}\n"
            f"Functions: {index['statistics']['total_functions']}\n"
            f"Classes: {index['statistics']['total_classes']}\n"
            f"Languages: {', '.join(index['statistics']['languages'])}"
        )
    
    def _analyze_file(self, file_path: Path, content: str) -> Dict[str, Any]:
        """Analyze a single file and extract information."""
        info = {
            "path": str(file_path),
            "size": len(content),
            "lines": len(content.split('\n')),
            "language": self._detect_file_language(file_path),
            "functions": [],
            "classes": [],
            "imports": [],
            "complexity": 0
        }
        
        # Language-specific analysis
        if file_path.suffix == '.py':
            info.update(self._analyze_python_file(content))
        elif file_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
            info.update(self._analyze_javascript_file(content))
        
        return info
    
    def _analyze_python_file(self, content: str) -> Dict[str, Any]:
        """Analyze Python file using AST."""
        try:
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append({
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "signature": f"def {node.name}({', '.join(arg.arg for arg in node.args.args)})"
                    })
                
                elif isinstance(node, ast.ClassDef):
                    methods = []
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            methods.append(item.name)
                    
                    classes.append({
                        "name": node.name,
                        "line": node.lineno,
                        "methods": methods,
                        "bases": [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]
                    })
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        module = node.module or ""
                        for alias in node.names:
                            imports.append(f"{module}.{alias.name}")
            
            return {
                "functions": functions,
                "classes": classes,
                "imports": imports
            }
            
        except SyntaxError:
            return {"functions": [], "classes": [], "imports": []}
    
    def _analyze_javascript_file(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript file using regex patterns."""
        functions = []
        classes = []
        imports = []
        
        # Function patterns
        func_patterns = [
            r'function\s+(\w+)\s*\(',
            r'const\s+(\w+)\s*=\s*\(',
            r'let\s+(\w+)\s*=\s*\(',
            r'var\s+(\w+)\s*=\s*\(',
            r'(\w+)\s*:\s*function\s*\(',
            r'(\w+)\s*\([^)]*\)\s*=>\s*{'
        ]
        
        for pattern in func_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                functions.append({
                    "name": match.group(1),
                    "line": line_num,
                    "signature": match.group(0)
                })
        
        # Class patterns
        class_pattern = r'class\s+(\w+)'
        for match in re.finditer(class_pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            classes.append({
                "name": match.group(1),
                "line": line_num,
                "methods": []
            })
        
        # Import patterns
        import_patterns = [
            r'import\s+.*\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'import\s+[\'"]([^\'"]+)[\'"]',
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)'
        ]
        
        for pattern in import_patterns:
            for match in re.finditer(pattern, content):
                imports.append(match.group(1))
        
        return {
            "functions": functions,
            "classes": classes,
            "imports": imports
        }
    
    def _detect_file_language(self, file_path: Path) -> str:
        """Detect programming language from file extension."""
        suffix = file_path.suffix.lower()
        
        for lang, extensions in self._language_extensions.items():
            if suffix in extensions:
                return lang
        
        return "unknown"
    
    def _detect_languages(self, files: List[Path]) -> List[str]:
        """Detect all languages in the file list."""
        languages = set()
        
        for file_path in files:
            lang = self._detect_file_language(file_path)
            if lang != "unknown":
                languages.add(lang)
        
        return sorted(list(languages))
    
    def _search_code(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Search for code patterns in the codebase."""
        path = params.get("path", ".")
        query = params.get("query", "")
        case_sensitive = params.get("case_sensitive", False)
        
        if not query:
            yield self.create_response("Query is required for search action")
            return
        
        yield self.create_response(f"Searching for: {query}")
        
        files = self._get_file_list(path, params)
        results = []
        
        # Compile regex pattern
        flags = 0 if case_sensitive else re.IGNORECASE
        try:
            pattern = re.compile(query, flags)
        except re.error:
            # Fallback to literal search
            pattern = re.compile(re.escape(query), flags)
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                for line_num, line in enumerate(lines, 1):
                    if pattern.search(line):
                        results.append({
                            "file": str(file_path),
                            "line": line_num,
                            "content": line.strip(),
                            "context": self._get_line_context(lines, line_num - 1, 2)
                        })
                        
                        # Limit results
                        if len(results) >= 100:
                            break
                
                if len(results) >= 100:
                    break
                    
            except Exception as e:
                logger.warning(f"Error searching {file_path}: {e}")
        
        # Format results
        if results:
            formatted_results = []
            for result in results:
                formatted_results.append(
                    f"📁 {result['file']}:{result['line']}\n"
                    f"   {result['content']}\n"
                )
            
            yield self.create_response(
                f"Found {len(results)} matches:\n\n" + "\n".join(formatted_results)
            )
        else:
            yield self.create_response("No matches found")
    
    def _get_line_context(self, lines: List[str], line_index: int, context_size: int) -> List[str]:
        """Get context lines around a specific line."""
        start = max(0, line_index - context_size)
        end = min(len(lines), line_index + context_size + 1)
        return [line.strip() for line in lines[start:end]]
    
    def _grep_files(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform grep-like search in files."""
        path = params.get("path", ".")
        query = params.get("query", "")
        
        if not query:
            yield self.create_response("Query is required for grep action")
            return
        
        try:
            # Use system grep if available
            cmd = ["grep", "-rn", query, path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                yield self.create_response(f"Grep results:\n\n{result.stdout}")
            else:
                # Fallback to Python search
                yield from self._search_code(params)
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            # Fallback to Python search
            yield from self._search_code(params)
    
    def _find_files(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Find files matching patterns."""
        path = params.get("path", ".")
        query = params.get("query", "")
        
        yield self.create_response(f"Finding files matching: {query}")
        
        files = self._get_file_list(path, params)
        
        if query:
            # Filter files by name pattern
            pattern = re.compile(query, re.IGNORECASE)
            matching_files = [f for f in files if pattern.search(f.name)]
        else:
            matching_files = files
        
        if matching_files:
            file_list = "\n".join(f"📄 {f}" for f in matching_files[:50])
            if len(matching_files) > 50:
                file_list += f"\n... and {len(matching_files) - 50} more files"
            
            yield self.create_response(
                f"Found {len(matching_files)} files:\n\n{file_list}"
            )
        else:
            yield self.create_response("No matching files found")
    
    def _analyze_codebase(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform comprehensive codebase analysis."""
        path = params.get("path", ".")
        
        yield self.create_response(f"Analyzing codebase at: {path}")
        
        files = self._get_file_list(path, params)
        
        analysis = {
            "overview": {
                "total_files": len(files),
                "total_size": 0,
                "total_lines": 0,
                "languages": defaultdict(int)
            },
            "structure": {
                "directories": set(),
                "file_types": defaultdict(int)
            },
            "code_quality": {
                "large_files": [],
                "complex_functions": [],
                "duplicate_code": []
            }
        }
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Update statistics
                analysis["overview"]["total_size"] += len(content)
                lines = len(content.split('\n'))
                analysis["overview"]["total_lines"] += lines
                
                # Language detection
                lang = self._detect_file_language(file_path)
                analysis["overview"]["languages"][lang] += 1
                
                # File type
                analysis["structure"]["file_types"][file_path.suffix] += 1
                
                # Directory structure
                analysis["structure"]["directories"].add(str(file_path.parent))
                
                # Code quality checks
                if lines > 500:
                    analysis["code_quality"]["large_files"].append({
                        "file": str(file_path),
                        "lines": lines
                    })
                
            except Exception as e:
                logger.warning(f"Error analyzing {file_path}: {e}")
        
        # Format analysis results
        overview = analysis["overview"]
        structure = analysis["structure"]
        quality = analysis["code_quality"]
        
        result = f"""
📊 Codebase Analysis Results

📈 Overview:
- Total Files: {overview['total_files']:,}
- Total Size: {overview['total_size']:,} bytes
- Total Lines: {overview['total_lines']:,}

🗂️ Languages:
{chr(10).join(f"- {lang}: {count} files" for lang, count in overview['languages'].items())}

📁 Structure:
- Directories: {len(structure['directories'])}
- File Types: {len(structure['file_types'])}

⚠️ Code Quality:
- Large Files (>500 lines): {len(quality['large_files'])}
"""
        
        if quality["large_files"]:
            result += "\n\n📄 Large Files:\n"
            for file_info in quality["large_files"][:10]:
                result += f"- {file_info['file']} ({file_info['lines']} lines)\n"
        
        yield self.create_response(result)
    
    def _analyze_dependencies(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze project dependencies."""
        path = params.get("path", ".")
        
        yield self.create_response(f"Analyzing dependencies in: {path}")
        
        base_path = Path(path)
        dependencies = {}
        
        # Check for common dependency files
        dep_files = {
            "requirements.txt": "Python",
            "package.json": "Node.js",
            "Cargo.toml": "Rust",
            "go.mod": "Go",
            "pom.xml": "Java (Maven)",
            "build.gradle": "Java (Gradle)",
            "composer.json": "PHP"
        }
        
        for dep_file, tech in dep_files.items():
            file_path = base_path / dep_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    dependencies[tech] = {
                        "file": dep_file,
                        "content": content[:1000] + "..." if len(content) > 1000 else content
                    }
                except Exception as e:
                    logger.warning(f"Error reading {dep_file}: {e}")
        
        if dependencies:
            result = "📦 Dependencies Found:\n\n"
            for tech, info in dependencies.items():
                result += f"🔧 {tech} ({info['file']}):\n"
                result += f"```\n{info['content']}\n```\n\n"
            
            yield self.create_response(result)
        else:
            yield self.create_response("No dependency files found")
    
    def _suggest_refactoring(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Suggest code refactoring opportunities."""
        path = params.get("path", ".")
        
        yield self.create_response(f"Analyzing refactoring opportunities in: {path}")
        
        # This is a simplified implementation
        # In a real implementation, you would use more sophisticated analysis
        
        suggestions = [
            "🔄 Consider extracting large functions into smaller, more focused functions",
            "📦 Group related functionality into modules or classes",
            "🧹 Remove duplicate code by creating reusable functions",
            "📝 Add type hints and documentation for better maintainability",
            "⚡ Optimize performance-critical sections",
            "🛡️ Add error handling and input validation",
            "🧪 Increase test coverage for critical components"
        ]
        
        yield self.create_response(
            "💡 Refactoring Suggestions:\n\n" + "\n".join(suggestions)
        )
    
    def _calculate_metrics(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Calculate code metrics."""
        path = params.get("path", ".")
        
        yield self.create_response(f"Calculating code metrics for: {path}")
        
        files = self._get_file_list(path, params)
        
        metrics = {
            "files": len(files),
            "total_lines": 0,
            "code_lines": 0,
            "comment_lines": 0,
            "blank_lines": 0,
            "functions": 0,
            "classes": 0,
            "complexity": 0
        }
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                metrics["total_lines"] += len(lines)
                
                for line in lines:
                    stripped = line.strip()
                    if not stripped:
                        metrics["blank_lines"] += 1
                    elif stripped.startswith('#') or stripped.startswith('//'):
                        metrics["comment_lines"] += 1
                    else:
                        metrics["code_lines"] += 1
                
                # Analyze file for functions and classes
                content = ''.join(lines)
                file_info = self._analyze_file(file_path, content)
                metrics["functions"] += len(file_info.get("functions", []))
                metrics["classes"] += len(file_info.get("classes", []))
                
            except Exception as e:
                logger.warning(f"Error calculating metrics for {file_path}: {e}")
        
        # Calculate derived metrics
        if metrics["total_lines"] > 0:
            code_ratio = metrics["code_lines"] / metrics["total_lines"] * 100
            comment_ratio = metrics["comment_lines"] / metrics["total_lines"] * 100
        else:
            code_ratio = comment_ratio = 0
        
        result = f"""
📊 Code Metrics

📈 Line Counts:
- Total Lines: {metrics['total_lines']:,}
- Code Lines: {metrics['code_lines']:,} ({code_ratio:.1f}%)
- Comment Lines: {metrics['comment_lines']:,} ({comment_ratio:.1f}%)
- Blank Lines: {metrics['blank_lines']:,}

🏗️ Structure:
- Files: {metrics['files']:,}
- Functions: {metrics['functions']:,}
- Classes: {metrics['classes']:,}

📏 Ratios:
- Lines per File: {metrics['total_lines'] / max(metrics['files'], 1):.1f}
- Functions per File: {metrics['functions'] / max(metrics['files'], 1):.1f}
- Classes per File: {metrics['classes'] / max(metrics['files'], 1):.1f}
"""
        
        yield self.create_response(result)


# Create tool instance
codebase_tools = CodebaseToolsSpec()
