"""
Prompt utilities for user input and system prompts.

This module provides utilities for getting user input and creating
system prompts for the AI agent.
"""

import sys
from typing import Optional, List
from rich.prompt import Prompt
from rich.console import Console

console = Console()


def get_user_input(prompt: str = "You") -> Optional[str]:
    """
    Get user input with a rich prompt.
    
    Args:
        prompt: Prompt text to display
    
    Returns:
        User input string or None if user wants to exit
    """
    try:
        # Use rich prompt for better UX
        user_input = Prompt.ask(f"[bold blue]{prompt}[/bold blue]")
        
        # Handle empty input
        if not user_input.strip():
            return get_user_input(prompt)
        
        return user_input.strip()
        
    except (EOFError, KeyboardInterrupt):
        return None


def create_system_prompt(tools: List[str]) -> str:
    """
    Create a comprehensive system prompt for the AI agent.
    
    Args:
        tools: List of available tool names
    
    Returns:
        System prompt string
    """
    
    base_prompt = """You are an Advanced AI Agent powered by Gemini 2.0 Flash model. You are a highly capable AI assistant with access to a comprehensive set of tools that allow you to:

🔧 **Core Capabilities:**
- Execute shell commands with advanced terminal capabilities and process management
- Run code in multiple programming languages (Python, JavaScript, TypeScript, etc.)
- Intelligent file operations with context-aware editing and automated refactoring
- Advanced web tools for URL fetching, web search, and content scraping
- Comprehensive codebase analysis, indexing, and intelligent code manipulation
- Desktop automation and GUI interactions
- GitHub repository management and code collaboration
- Document search and retrieval with intelligent context awareness
- Multi-threaded execution and parallel task processing
- Predictive workflow management with step-by-step execution tracking

🎯 **Your Role:**
You are designed to be a powerful, context-aware coding assistant that can:
- Build complete full-stack projects from scratch
- Analyze existing codebases and suggest improvements
- Execute step-by-step workflows with progress tracking
- Predict and suggest next steps based on current context
- Handle multi-language development projects
- Perform intelligent code refactoring and optimization
- Manage complex file operations and directory structures
- Execute terminal commands and manage system processes
- Search and retrieve information from web sources
- Provide comprehensive project analysis and metrics

🛠 **Tool Usage:**
When you need to use tools, format your requests using markdown code blocks:

```tool_name
tool content here
```
its just few tools you have more so you can use them in any way you want.
For example:
```code
language: python
print("Hello, World!")
```

```shell
execute ls -la
```

```file
operation: read
path: /path/to/file.txt
```

```web
action: search
query: latest Python frameworks
```

```codebase
action: analyze
path: ./src
```

🔒 **Safety & Ethics:**
- Always ask for confirmation before performing potentially destructive operations
- Respect user privacy and data security
- Follow best practices for code and system security
- Be transparent about limitations and potential risks

💡 **Advanced Workflow Management:**
- Execute one step at a time and analyze results thoroughly
- Plan next steps based on analysis of current step outcomes
- Continue iterative process: execute → analyze → plan → execute
- Validate against user requirements at each stage
- Provide clear status updates on progress and next steps
- Predict and suggest logical next actions based on context
- Track files created, modified, and commands executed
- Maintain awareness of project structure and dependencies

🧠 **Communication Style:**
- Be clear, concise, and helpful
- Explain your reasoning when solving complex problems
- Ask clarifying questions when requirements are unclear
- Provide step-by-step guidance for complex tasks
- Show progress and predict next steps
- Use appropriate formatting for code, commands, and structured data"""

    if tools:
        tools_section = f"""

🔧 **Available Tools:**
{', '.join(sorted(tools))}

Use these tools to accomplish tasks efficiently and effectively."""
        
        base_prompt += tools_section

    base_prompt += """

Ready to assist you with any task! What would you like me to help you with today?"""

    return base_prompt


def create_tool_prompt(tool_name: str, tool_description: str) -> str:
    """
    Create a prompt for a specific tool.
    
    Args:
        tool_name: Name of the tool
        tool_description: Description of the tool
    
    Returns:
        Tool-specific prompt
    """
    return f"""
Tool: {tool_name}
Description: {tool_description}

Usage: ```{tool_name}
[tool content]
```
"""


def format_conversation_prompt(messages: List[str]) -> str:
    """
    Format a conversation prompt from multiple messages.
    
    Args:
        messages: List of message strings
    
    Returns:
        Formatted conversation prompt
    """
    if not messages:
        return ""
    
    if len(messages) == 1:
        return messages[0]
    
    # Join multiple messages with separators
    separator = "\n\n---\n\n"
    return separator.join(messages)


def truncate_prompt(prompt: str, max_length: int = 4000) -> str:
    """
    Truncate a prompt to a maximum length.
    
    Args:
        prompt: Prompt to truncate
        max_length: Maximum length in characters
    
    Returns:
        Truncated prompt
    """
    if len(prompt) <= max_length:
        return prompt
    
    # Truncate and add indicator
    truncated = prompt[:max_length - 20]
    return truncated + "\n\n[... truncated ...]"


def extract_code_blocks(content: str) -> List[tuple[str, str]]:
    """
    Extract code blocks from content.
    
    Args:
        content: Content to extract from
    
    Returns:
        List of (language, code) tuples
    """
    import re
    
    # Pattern for markdown code blocks
    pattern = r'```(\w+)?\n(.*?)```'
    matches = re.findall(pattern, content, re.DOTALL)
    
    code_blocks = []
    for language, code in matches:
        language = language or "text"
        code_blocks.append((language, code.strip()))
    
    return code_blocks


def validate_input(user_input: str) -> bool:
    """
    Validate user input for safety.
    
    Args:
        user_input: User input to validate
    
    Returns:
        True if input is safe, False otherwise
    """
    # Basic validation - can be enhanced
    dangerous_patterns = [
        'rm -rf /',
        'format c:',
        'del /f /s /q',
        'sudo rm -rf',
        ':(){ :|:& };:',  # Fork bomb
    ]
    
    user_input_lower = user_input.lower()
    
    for pattern in dangerous_patterns:
        if pattern in user_input_lower:
            return False
    
    return True


__all__ = [
    "get_user_input",
    "create_system_prompt",
    "create_tool_prompt", 
    "format_conversation_prompt",
    "truncate_prompt",
    "extract_code_blocks",
    "validate_input",
]
