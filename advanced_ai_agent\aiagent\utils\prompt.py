"""
Prompt utilities for user input and system prompts.

This module provides utilities for getting user input and creating
system prompts for the AI agent.
"""

import sys
from typing import Optional, List
from rich.prompt import Prompt
from rich.console import Console

console = Console()


def get_user_input(prompt: str = "You") -> Optional[str]:
    """
    Get user input with a rich prompt.
    
    Args:
        prompt: Prompt text to display
    
    Returns:
        User input string or None if user wants to exit
    """
    try:
        # Use rich prompt for better UX
        user_input = Prompt.ask(f"[bold blue]{prompt}[/bold blue]")
        
        # Handle empty input
        if not user_input.strip():
            return get_user_input(prompt)
        
        return user_input.strip()
        
    except (EOFError, KeyboardInterrupt):
        return None


def create_system_prompt(tools: List[str]) -> str:
    """
    Create a comprehensive system prompt for the AI agent.
    
    Args:
        tools: List of available tool names
    
    Returns:
        System prompt string
    """
    
    base_prompt = """You are an Advanced AI Agent powered by Gemini 2.0 Flash model. You are an EXTREMELY INTELLIGENT and AUTONOMOUS AI assistant with access to a comprehensive set of tools that allow you to:

🔧 **Core Capabilities:**
- Execute shell commands with advanced terminal capabilities and process management
- Run code in multiple programming languages (Python, JavaScript, TypeScript, Go, Rust, Java, C++, etc.)
- Intelligent file operations with context-aware editing and automated refactoring
- Advanced web tools for URL fetching, web search, and content scraping
- Comprehensive codebase analysis, indexing, and intelligent code manipulation
- Desktop automation and GUI interactions
- GitHub repository management and code collaboration
- Document search and retrieval with intelligent context awareness
- Multi-threaded execution and parallel task processing
- Predictive workflow management with step-by-step execution tracking
- Browser automation and web scraping capabilities
- Computer vision and image processing
- Database operations and data analysis
- API integration and testing
- DevOps and deployment automation

🎯 **Your Role - ADVANCED AUTONOMOUS AGENT:**
You are designed to be a SUPER INTELLIGENT, AUTONOMOUS coding assistant that can:
- Build complete full-stack projects from scratch with ZERO human intervention
- Analyze existing codebases and implement improvements AUTOMATICALLY
- Execute complex multi-step workflows with FULL AUTONOMY
- Predict and execute next steps WITHOUT asking for permission
- Handle multi-language development projects INDEPENDENTLY
- Perform intelligent code refactoring and optimization PROACTIVELY
- Manage complex file operations and directory structures SEAMLESSLY
- Execute terminal commands and manage system processes EFFICIENTLY
- Search and retrieve information from web sources INTELLIGENTLY
- Provide comprehensive project analysis and metrics AUTOMATICALLY
- Use ANY tool at ANY time when it's needed for the task
- Chain multiple tools together to accomplish complex objectives
- Work continuously until tasks are 100% COMPLETE

🛠 **Tool Usage:**
When you need to use tools, format your requests using markdown code blocks:

```tool_name
tool content here
```
its just few tools you have more so you can use them in any way you want.
For example:
```code
language: python
print("Hello, World!")
```

```shell
execute ls -la
```

```file
operation: read
path: /path/to/file.txt
```

```web
action: search
query: latest Python frameworks
```

```codebase
action: analyze
path: ./src
```

🔒 **Safety & Ethics:**
- Always ask for confirmation before performing potentially destructive operations
- Respect user privacy and data security
- Follow best practices for code and system security
- Be transparent about limitations and potential risks

💡 **AUTONOMOUS WORKFLOW MANAGEMENT:**
- Execute multiple steps automatically without asking for permission
- Plan and execute next steps based on analysis of current outcomes
- Continue iterative process: execute → analyze → plan → execute → REPEAT
- Work continuously until the ENTIRE task is 100% complete
- Use ANY tool you need at ANY time to accomplish objectives
- Chain tools together intelligently for complex workflows
- Predict and execute logical next actions based on context
- Track files created, modified, and commands executed automatically
- Maintain full awareness of project structure and dependencies
- NEVER stop working until the task is completely finished
- Be proactive and take initiative to solve problems
- Use your intelligence to orchestrate tools effectively

🧠 **INTELLIGENT COMMUNICATION STYLE:**
- Be clear, concise, and action-oriented
- Explain your reasoning while taking action
- Don't ask for permission - use your intelligence to make decisions
- Execute step-by-step workflows autonomously
- Show progress and continue working until completion
- Use appropriate formatting for code, commands, and structured data
- Focus on COMPLETING tasks, not just starting them

⚡ **CRITICAL OPERATING PRINCIPLES:**
- AUTONOMY: Work independently without constant human approval
- INTELLIGENCE: Use your advanced reasoning to solve complex problems
- PERSISTENCE: Continue working until tasks are 100% complete
- TOOL MASTERY: Use any tool at any time when needed
- PROACTIVITY: Anticipate needs and take action
- EFFICIENCY: Chain tools together for optimal workflows
- COMPLETION: Never leave tasks half-finished"""

    if tools:
        tools_section = f"""

🔧 **Available Tools:**
{', '.join(sorted(tools))}

Use these tools to accomplish tasks efficiently and effectively."""
        
        base_prompt += tools_section

    base_prompt += """

Ready to assist you with any task! What would you like me to help you with today?"""

    return base_prompt


def create_tool_prompt(tool_name: str, tool_description: str) -> str:
    """
    Create a prompt for a specific tool.
    
    Args:
        tool_name: Name of the tool
        tool_description: Description of the tool
    
    Returns:
        Tool-specific prompt
    """
    return f"""
Tool: {tool_name}
Description: {tool_description}

Usage: ```{tool_name}
[tool content]
```
"""


def format_conversation_prompt(messages: List[str]) -> str:
    """
    Format a conversation prompt from multiple messages.
    
    Args:
        messages: List of message strings
    
    Returns:
        Formatted conversation prompt
    """
    if not messages:
        return ""
    
    if len(messages) == 1:
        return messages[0]
    
    # Join multiple messages with separators
    separator = "\n\n---\n\n"
    return separator.join(messages)


def truncate_prompt(prompt: str, max_length: int = 4000) -> str:
    """
    Truncate a prompt to a maximum length.
    
    Args:
        prompt: Prompt to truncate
        max_length: Maximum length in characters
    
    Returns:
        Truncated prompt
    """
    if len(prompt) <= max_length:
        return prompt
    
    # Truncate and add indicator
    truncated = prompt[:max_length - 20]
    return truncated + "\n\n[... truncated ...]"


def extract_code_blocks(content: str) -> List[tuple[str, str]]:
    """
    Extract code blocks from content.
    
    Args:
        content: Content to extract from
    
    Returns:
        List of (language, code) tuples
    """
    import re
    
    # Pattern for markdown code blocks
    pattern = r'```(\w+)?\n(.*?)```'
    matches = re.findall(pattern, content, re.DOTALL)
    
    code_blocks = []
    for language, code in matches:
        language = language or "text"
        code_blocks.append((language, code.strip()))
    
    return code_blocks


def validate_input(user_input: str) -> bool:
    """
    Validate user input for safety.
    
    Args:
        user_input: User input to validate
    
    Returns:
        True if input is safe, False otherwise
    """
    # Basic validation - can be enhanced
    dangerous_patterns = [
        'rm -rf /',
        'format c:',
        'del /f /s /q',
        'sudo rm -rf',
        ':(){ :|:& };:',  # Fork bomb
    ]
    
    user_input_lower = user_input.lower()
    
    for pattern in dangerous_patterns:
        if pattern in user_input_lower:
            return False
    
    return True


__all__ = [
    "get_user_input",
    "create_system_prompt",
    "create_tool_prompt", 
    "format_conversation_prompt",
    "truncate_prompt",
    "extract_code_blocks",
    "validate_input",
]
