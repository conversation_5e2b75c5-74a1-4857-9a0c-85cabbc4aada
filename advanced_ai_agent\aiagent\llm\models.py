"""
Model information and metadata.

This module provides model information, capabilities, and pricing data
for different LLM models, particularly Gemini models.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum


class ModelCapability(Enum):
    """Model capabilities."""
    TEXT = "text"
    VISION = "vision"
    AUDIO = "audio"
    FUNCTION_CALLING = "function_calling"
    STREAMING = "streaming"
    MULTIMODAL = "multimodal"


@dataclass
class ModelInfo:
    """Information about a language model."""
    name: str
    full_name: str
    provider: str
    max_tokens: int
    context_window: int
    capabilities: List[ModelCapability]
    input_cost_per_token: float  # USD per token
    output_cost_per_token: float  # USD per token
    supports_streaming: bool = True
    supports_function_calling: bool = True
    supports_vision: bool = False
    supports_audio: bool = False
    description: str = ""
    
    @property
    def supports_multimodal(self) -> bool:
        """Check if model supports multimodal input."""
        return self.supports_vision or self.supports_audio
    
    def estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost for given token counts."""
        input_cost = input_tokens * self.input_cost_per_token
        output_cost = output_tokens * self.output_cost_per_token
        return input_cost + output_cost


# Gemini model definitions
GEMINI_MODELS: Dict[str, ModelInfo] = {
    "gemini-2.0-flash-exp": ModelInfo(
        name="gemini-2.0-flash-exp",
        full_name="Gemini 2.0 Flash (Experimental)",
        provider="google",
        max_tokens=8192,
        context_window=1000000,  # 1M tokens
        capabilities=[
            ModelCapability.TEXT,
            ModelCapability.VISION,
            ModelCapability.AUDIO,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
            ModelCapability.MULTIMODAL,
        ],
        input_cost_per_token=0.000000075,  # $0.075 per 1M tokens
        output_cost_per_token=0.0000003,   # $0.30 per 1M tokens
        supports_streaming=True,
        supports_function_calling=True,
        supports_vision=True,
        supports_audio=True,
        description="Latest Gemini 2.0 Flash model with multimodal capabilities and improved performance"
    ),
    
    "gemini-1.5-flash": ModelInfo(
        name="gemini-1.5-flash",
        full_name="Gemini 1.5 Flash",
        provider="google",
        max_tokens=8192,
        context_window=1000000,
        capabilities=[
            ModelCapability.TEXT,
            ModelCapability.VISION,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
            ModelCapability.MULTIMODAL,
        ],
        input_cost_per_token=0.000000075,
        output_cost_per_token=0.0000003,
        supports_streaming=True,
        supports_function_calling=True,
        supports_vision=True,
        supports_audio=False,
        description="Fast and efficient Gemini model with vision capabilities"
    ),
    
    "gemini-1.5-pro": ModelInfo(
        name="gemini-1.5-pro",
        full_name="Gemini 1.5 Pro",
        provider="google",
        max_tokens=8192,
        context_window=2000000,  # 2M tokens
        capabilities=[
            ModelCapability.TEXT,
            ModelCapability.VISION,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
            ModelCapability.MULTIMODAL,
        ],
        input_cost_per_token=0.00000125,   # $1.25 per 1M tokens
        output_cost_per_token=0.000005,    # $5.00 per 1M tokens
        supports_streaming=True,
        supports_function_calling=True,
        supports_vision=True,
        supports_audio=False,
        description="Most capable Gemini model with largest context window"
    ),
    
    "gemini-1.0-pro": ModelInfo(
        name="gemini-1.0-pro",
        full_name="Gemini 1.0 Pro",
        provider="google",
        max_tokens=8192,
        context_window=32768,
        capabilities=[
            ModelCapability.TEXT,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
        ],
        input_cost_per_token=0.0000005,    # $0.50 per 1M tokens
        output_cost_per_token=0.0000015,   # $1.50 per 1M tokens
        supports_streaming=True,
        supports_function_calling=True,
        supports_vision=False,
        supports_audio=False,
        description="Original Gemini Pro model for text-only tasks"
    ),
}


def get_model_info(model_name: str) -> ModelInfo:
    """
    Get model information by name.
    
    Args:
        model_name: Name of the model
    
    Returns:
        ModelInfo object
    
    Raises:
        ValueError: If model is not found
    """
    if model_name in GEMINI_MODELS:
        return GEMINI_MODELS[model_name]
    
    # Try to find by partial name
    for name, info in GEMINI_MODELS.items():
        if model_name in name or name in model_name:
            return info
    
    raise ValueError(f"Model '{model_name}' not found. Available models: {list(GEMINI_MODELS.keys())}")


def list_models() -> List[str]:
    """List all available model names."""
    return list(GEMINI_MODELS.keys())


def get_default_model() -> ModelInfo:
    """Get the default model."""
    return GEMINI_MODELS["gemini-2.0-flash-exp"]


def get_models_by_capability(capability: ModelCapability) -> List[ModelInfo]:
    """Get models that support a specific capability."""
    return [model for model in GEMINI_MODELS.values() if capability in model.capabilities]


def get_cheapest_model() -> ModelInfo:
    """Get the cheapest model based on input cost."""
    return min(GEMINI_MODELS.values(), key=lambda m: m.input_cost_per_token)


def get_most_capable_model() -> ModelInfo:
    """Get the model with the most capabilities."""
    return max(GEMINI_MODELS.values(), key=lambda m: len(m.capabilities))


def get_largest_context_model() -> ModelInfo:
    """Get the model with the largest context window."""
    return max(GEMINI_MODELS.values(), key=lambda m: m.context_window)


def estimate_tokens_simple(text: str) -> int:
    """
    Simple token estimation (approximately 4 characters per token).
    
    Args:
        text: Text to estimate tokens for
    
    Returns:
        Estimated token count
    """
    return len(text) // 4


def format_model_info(model: ModelInfo) -> str:
    """Format model information for display."""
    capabilities = ", ".join([cap.value for cap in model.capabilities])
    
    return f"""
Model: {model.full_name}
Provider: {model.provider}
Max Tokens: {model.max_tokens:,}
Context Window: {model.context_window:,}
Capabilities: {capabilities}
Input Cost: ${model.input_cost_per_token * 1_000_000:.2f} per 1M tokens
Output Cost: ${model.output_cost_per_token * 1_000_000:.2f} per 1M tokens
Description: {model.description}
""".strip()


__all__ = [
    "ModelInfo",
    "ModelCapability",
    "GEMINI_MODELS",
    "get_model_info",
    "list_models",
    "get_default_model",
    "get_models_by_capability",
    "get_cheapest_model",
    "get_most_capable_model",
    "get_largest_context_model",
    "estimate_tokens_simple",
    "format_model_info",
]
